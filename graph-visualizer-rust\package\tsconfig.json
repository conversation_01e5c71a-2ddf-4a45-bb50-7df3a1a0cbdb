{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/graph": ["src/"],
      "@/graph/*": ["src/*"],
    },
    "plugins": [{
      "transform": "@zerollup/ts-transform-paths",
      "exclude": ["*"]
      }],
    "target": "es2019",
    "module": "esnext",
    "moduleResolution": "node",
    "lib": ["es2019", "es2017", "es7", "es6", "dom"],
    "declaration": true,
    "outDir": "dist",

    "alwaysStrict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictPropertyInitialization": true,
    "strictFunctionTypes": true,
    "noImplicitThis": true,
    "strictBindCallApply": true,

    "noPropertyAccessFromIndexSignature": true,
    "noUncheckedIndexedAccess": true,

    "esModuleInterop": true
  },
  "include": [
    "./src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}