{"version": 3, "file": "events.js", "sources": ["../../../../../../node_modules/@material/list/events.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar ELEMENTS_KEY_ALLOWED_IN = ['input', 'button', 'textarea', 'select'];\n/**\n * Ensures that preventDefault is only called if the containing element\n * doesn't consume the event, and it will cause an unintended scroll.\n *\n * @param evt keyboard event to be prevented.\n */\nexport var preventDefaultEvent = function (evt) {\n    var target = evt.target;\n    if (!target) {\n        return;\n    }\n    var tagName = (\"\" + target.tagName).toLowerCase();\n    if (ELEMENTS_KEY_ALLOWED_IN.indexOf(tagName) === -1) {\n        evt.preventDefault();\n    }\n};\n//# sourceMappingURL=events.js.map"], "names": ["ELEMENTS_KEY_ALLOWED_IN", "preventDefaultEvent", "evt", "target", "tagName", "toLowerCase", "indexOf", "preventDefault"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAsBA,IAAIA,EAA0B,CAAC,QAAS,SAAU,WAAY,UAOnDC,EAAsB,SAAUC,GACvC,IAAIC,EAASD,EAAIC,OACjB,GAAKA,EAAL,CAGA,IAAIC,GAAW,GAAKD,EAAOC,SAASC,eACc,IAA9CL,EAAwBM,QAAQF,IAChCF,EAAIK,gBAHP,CAKL"}