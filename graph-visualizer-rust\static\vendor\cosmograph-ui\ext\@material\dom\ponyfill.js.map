{"version": 3, "file": "ponyfill.js", "sources": ["../../../../../../node_modules/@material/dom/ponyfill.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n/**\n * @fileoverview A \"ponyfill\" is a polyfill that doesn't modify the global prototype chain.\n * This makes ponyfills safer than traditional polyfills, especially for libraries like MDC.\n */\nexport function closest(element, selector) {\n    if (element.closest) {\n        return element.closest(selector);\n    }\n    var el = element;\n    while (el) {\n        if (matches(el, selector)) {\n            return el;\n        }\n        el = el.parentElement;\n    }\n    return null;\n}\nexport function matches(element, selector) {\n    var nativeMatches = element.matches\n        || element.webkitMatchesSelector\n        || element.msMatchesSelector;\n    return nativeMatches.call(element, selector);\n}\n/**\n * Used to compute the estimated scroll width of elements. When an element is\n * hidden due to display: none; being applied to a parent element, the width is\n * returned as 0. However, the element will have a true width once no longer\n * inside a display: none context. This method computes an estimated width when\n * the element is hidden or returns the true width when the element is visble.\n * @param {Element} element the element whose width to estimate\n */\nexport function estimateScrollWidth(element) {\n    // Check the offsetParent. If the element inherits display: none from any\n    // parent, the offsetParent property will be null (see\n    // https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/offsetParent).\n    // This check ensures we only clone the node when necessary.\n    var htmlEl = element;\n    if (htmlEl.offsetParent !== null) {\n        return htmlEl.scrollWidth;\n    }\n    var clone = htmlEl.cloneNode(true);\n    clone.style.setProperty('position', 'absolute');\n    clone.style.setProperty('transform', 'translate(-9999px, -9999px)');\n    document.documentElement.appendChild(clone);\n    var scrollWidth = clone.scrollWidth;\n    document.documentElement.removeChild(clone);\n    return scrollWidth;\n}\n//# sourceMappingURL=ponyfill.js.map"], "names": ["closest", "element", "selector", "el", "matches", "parentElement", "webkitMatchesSelector", "msMatchesSelector", "call", "estimateScrollWidth", "htmlEl", "offsetParent", "scrollWidth", "clone", "cloneNode", "style", "setProperty", "document", "documentElement", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AA0BO,SAASA,EAAQC,EAASC,GAC7B,GAAID,EAAQD,QACR,OAAOC,EAAQD,QAAQE,GAG3B,IADA,IAAIC,EAAKF,EACFE,GAAI,CACP,GAAIC,EAAQD,EAAID,GACZ,OAAOC,EAEXA,EAAKA,EAAGE,aACX,CACD,OAAO,IACX,CACO,SAASD,EAAQH,EAASC,GAI7B,OAHoBD,EAAQG,SACrBH,EAAQK,uBACRL,EAAQM,mBACMC,KAAKP,EAASC,EACvC,CASO,SAASO,EAAoBR,GAKhC,IAAIS,EAAST,EACb,GAA4B,OAAxBS,EAAOC,aACP,OAAOD,EAAOE,YAElB,IAAIC,EAAQH,EAAOI,WAAU,GAC7BD,EAAME,MAAMC,YAAY,WAAY,YACpCH,EAAME,MAAMC,YAAY,YAAa,+BACrCC,SAASC,gBAAgBC,YAAYN,GACrC,IAAID,EAAcC,EAAMD,YAExB,OADAK,SAASC,gBAAgBE,YAAYP,GAC9BD,CACX"}