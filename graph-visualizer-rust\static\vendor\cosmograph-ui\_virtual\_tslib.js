var r=function(t,o){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(r[o]=t[o])},r(t,o)};function t(t,o){if("function"!=typeof o&&null!==o)throw new TypeError("Class extends value "+String(o)+" is not a constructor or null");function n(){this.constructor=t}r(t,o),t.prototype=null===o?Object.create(o):(n.prototype=o.prototype,new n)}var o=function(){return o=Object.assign||function(r){for(var t,o=1,n=arguments.length;o<n;o++)for(var e in t=arguments[o])Object.prototype.hasOwnProperty.call(t,e)&&(r[e]=t[e]);return r},o.apply(this,arguments)};function n(r){var t="function"==typeof Symbol&&Symbol.iterator,o=t&&r[t],n=0;if(o)return o.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function e(r,t){var o="function"==typeof Symbol&&r[Symbol.iterator];if(!o)return r;var n,e,i=o.call(r),l=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)l.push(n.value)}catch(r){e={error:r}}finally{try{n&&!n.done&&(o=i.return)&&o.call(i)}finally{if(e)throw e.error}}return l}function i(r,t,o){if(o||2===arguments.length)for(var n,e=0,i=t.length;e<i;e++)!n&&e in t||(n||(n=Array.prototype.slice.call(t,0,e)),n[e]=t[e]);return r.concat(n||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError;export{o as __assign,t as __extends,e as __read,i as __spreadArray,n as __values};
//# sourceMappingURL=_tslib.js.map
