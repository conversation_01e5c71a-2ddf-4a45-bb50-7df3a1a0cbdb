name: Build and Push Frontend Container

on:
  push:
    branches: [ main, feature/real-time-node-glow, feature/duckdb-realtime-updates ]
    paths:
      - 'frontend/**'
      - '.github/workflows/build-frontend.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'frontend/**'
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}-frontend

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        persist-credentials: false
        fetch-depth: 1
        submodules: false

    - name: Debug - List frontend directory contents
      run: |
        echo "=== Frontend directory structure ==="
        ls -la frontend/
        echo "=== Frontend src directory ==="
        ls -la frontend/src/
        echo "=== Frontend src/lib directory ==="
        ls -la frontend/src/lib/
        echo "=== Dockerignore files ==="
        find . -name "*.dockerignore" -o -name ".dockerignore"
        
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to the Container registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable=${{ github.ref == 'refs/heads/main' }}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        file: ./frontend/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64
        build-args: |
          BUILDKIT_PROGRESS=plain

    - name: Output image details
      if: success()
      run: |
        echo "✅ Image successfully built and pushed!"
        echo "📦 Registry: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}"
        echo "🏷️  Tags: ${{ steps.meta.outputs.tags }}"