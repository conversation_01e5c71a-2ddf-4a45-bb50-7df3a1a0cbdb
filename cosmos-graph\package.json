{"name": "@cosmos.gl/graph", "version": "2.3.1-beta.1", "description": "GPU-based force graph layout and rendering", "jsdelivr": "dist/index.min.js", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "MIT", "repository": "git://github.com/cosmos.gl/graph.git", "type": "module", "scripts": {"build": "rm -rf dist; rollup -c", "build:vite": "rm -rf dist; vite build", "watch": "rollup -c -w", "lint": "eslint --cache ./src --ext .ts --ignore-path .gitignore", "lint:staged": "npx lint-staged", "storybook": "storybook dev -p 6006", "build:storybook": "storybook build"}, "engines": {"node": ">=12.2.0", "npm": ">=7.0.0"}, "keywords": ["graph", "webgl", "force", "simulation", "visualization"], "homepage": "https://cosmosgl.github.io/graph", "author": "cosmos.gl", "devDependencies": {"@interacta/css-labels": "^0.1.2", "@jls-digital/storybook-addon-code": "^1.0.4", "@rollup/plugin-alias": "^3.1.9", "@rollup/plugin-commonjs": "^22.0.1", "@rollup/plugin-node-resolve": "^13.3.0", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.6", "@storybook/addon-essentials": "^8.4.5", "@storybook/addon-interactions": "^8.4.5", "@storybook/blocks": "^8.4.5", "@storybook/html": "^8.4.5", "@storybook/html-vite": "^8.4.5", "@storybook/test": "^8.4.5", "@types/d3-array": "^3.0.3", "@types/d3-color": "^3.1.0", "@types/d3-drag": "^3.0.7", "@types/d3-ease": "^3.0.0", "@types/d3-scale": "^4.0.2", "@types/d3-scale-chromatic": "^3.0.3", "@types/d3-selection": "^3.0.2", "@types/d3-transition": "^3.0.1", "@types/d3-zoom": "^3.0.1", "@types/dompurify": "^3.0.5", "@typescript-eslint/eslint-plugin": "^5.30.5", "@typescript-eslint/parser": "^5.30.5", "@zerollup/ts-transform-paths": "^1.7.18", "d3-scale-chromatic": "^3.1.0", "eslint": "^8.19.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-storybook": "^0.11.1", "eslint-plugin-unicorn": "^43.0.1", "lint-staged": "^13.0.3", "pre-commit": "^1.2.2", "remark-gfm": "^4.0.0", "rollup": "^2.76.0", "rollup-plugin-glslify": "^1.3.0", "storybook": "^8.4.5", "typescript": "^5.5.2", "vite": "^6.3.4", "vite-plugin-dts": "^4.3.0", "vite-plugin-glsl": "^1.3.0"}, "dependencies": {"d3-array": "^3.2.0", "d3-color": "^3.1.0", "d3-drag": "^3.0.0", "d3-ease": "^3.0.1", "d3-scale": "^4.0.2", "d3-selection": "^3.0.0", "d3-transition": "^3.0.1", "d3-zoom": "^3.0.0", "dompurify": "^3.2.6", "gl-bench": "^1.0.42", "gl-matrix": "^3.4.3", "random": "^4.1.0", "regl": "^2.1.0"}, "pre-commit": "lint:staged", "lint-staged": {"*.{js,ts}": "eslint --quiet --cache --fix"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}