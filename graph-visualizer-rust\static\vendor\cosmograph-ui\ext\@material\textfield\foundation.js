import{__extends as t,__values as e,__assign as i}from"../../../_virtual/_tslib.js";import{MDCFoundation as a}from"../base/foundation.js";import{ALWAYS_FLOAT_TYPES as n,VALIDATION_ATTR_WHITELIST as r,numbers as s,strings as o,cssClasses as l}from"./constants.js";
/**
 * @license
 * Copyright 2016 Google Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */var u=["mousedown","touchstart"],h=["click","keydown"],d=function(a){function d(t,e){void 0===e&&(e={});var n=a.call(this,i(i({},d.defaultAdapter),t))||this;return n.isFocused=!1,n.receivedUserInput=!1,n.valid=!0,n.useNativeValidation=!0,n.validateOnValueChange=!0,n.helperText=e.helperText,n.characterCounter=e.characterCounter,n.leadingIcon=e.leadingIcon,n.trailingIcon=e.trailingIcon,n.inputFocusHandler=function(){n.activateFocus()},n.inputBlurHandler=function(){n.deactivateFocus()},n.inputInputHandler=function(){n.handleInput()},n.setPointerXOffset=function(t){n.setTransformOrigin(t)},n.textFieldInteractionHandler=function(){n.handleTextFieldInteraction()},n.validationAttributeChangeHandler=function(t){n.handleValidationAttributeChange(t)},n}return t(d,a),Object.defineProperty(d,"cssClasses",{get:function(){return l},enumerable:!1,configurable:!0}),Object.defineProperty(d,"strings",{get:function(){return o},enumerable:!1,configurable:!0}),Object.defineProperty(d,"numbers",{get:function(){return s},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"shouldAlwaysFloat",{get:function(){var t=this.getNativeInput().type;return n.indexOf(t)>=0},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"shouldFloat",{get:function(){return this.shouldAlwaysFloat||this.isFocused||!!this.getValue()||this.isBadInput()},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"shouldShake",{get:function(){return!this.isFocused&&!this.isValid()&&!!this.getValue()},enumerable:!1,configurable:!0}),Object.defineProperty(d,"defaultAdapter",{get:function(){return{addClass:function(){},removeClass:function(){},hasClass:function(){return!0},setInputAttr:function(){},removeInputAttr:function(){},registerTextFieldInteractionHandler:function(){},deregisterTextFieldInteractionHandler:function(){},registerInputInteractionHandler:function(){},deregisterInputInteractionHandler:function(){},registerValidationAttributeChangeHandler:function(){return new MutationObserver((function(){}))},deregisterValidationAttributeChangeHandler:function(){},getNativeInput:function(){return null},isFocused:function(){return!1},activateLineRipple:function(){},deactivateLineRipple:function(){},setLineRippleTransformOrigin:function(){},shakeLabel:function(){},floatLabel:function(){},setLabelRequired:function(){},hasLabel:function(){return!1},getLabelWidth:function(){return 0},hasOutline:function(){return!1},notchOutline:function(){},closeOutline:function(){}}},enumerable:!1,configurable:!0}),d.prototype.init=function(){var t,i,a,n;this.adapter.hasLabel()&&this.getNativeInput().required&&this.adapter.setLabelRequired(!0),this.adapter.isFocused()?this.inputFocusHandler():this.adapter.hasLabel()&&this.shouldFloat&&(this.notchOutline(!0),this.adapter.floatLabel(!0),this.styleFloating(!0)),this.adapter.registerInputInteractionHandler("focus",this.inputFocusHandler),this.adapter.registerInputInteractionHandler("blur",this.inputBlurHandler),this.adapter.registerInputInteractionHandler("input",this.inputInputHandler);try{for(var r=e(u),s=r.next();!s.done;s=r.next()){var o=s.value;this.adapter.registerInputInteractionHandler(o,this.setPointerXOffset)}}catch(e){t={error:e}}finally{try{s&&!s.done&&(i=r.return)&&i.call(r)}finally{if(t)throw t.error}}try{for(var l=e(h),d=l.next();!d.done;d=l.next()){o=d.value;this.adapter.registerTextFieldInteractionHandler(o,this.textFieldInteractionHandler)}}catch(t){a={error:t}}finally{try{d&&!d.done&&(n=l.return)&&n.call(l)}finally{if(a)throw a.error}}this.validationObserver=this.adapter.registerValidationAttributeChangeHandler(this.validationAttributeChangeHandler),this.setcharacterCounter(this.getValue().length)},d.prototype.destroy=function(){var t,i,a,n;this.adapter.deregisterInputInteractionHandler("focus",this.inputFocusHandler),this.adapter.deregisterInputInteractionHandler("blur",this.inputBlurHandler),this.adapter.deregisterInputInteractionHandler("input",this.inputInputHandler);try{for(var r=e(u),s=r.next();!s.done;s=r.next()){var o=s.value;this.adapter.deregisterInputInteractionHandler(o,this.setPointerXOffset)}}catch(e){t={error:e}}finally{try{s&&!s.done&&(i=r.return)&&i.call(r)}finally{if(t)throw t.error}}try{for(var l=e(h),d=l.next();!d.done;d=l.next()){o=d.value;this.adapter.deregisterTextFieldInteractionHandler(o,this.textFieldInteractionHandler)}}catch(t){a={error:t}}finally{try{d&&!d.done&&(n=l.return)&&n.call(l)}finally{if(a)throw a.error}}this.adapter.deregisterValidationAttributeChangeHandler(this.validationObserver)},d.prototype.handleTextFieldInteraction=function(){var t=this.adapter.getNativeInput();t&&t.disabled||(this.receivedUserInput=!0)},d.prototype.handleValidationAttributeChange=function(t){var e=this;t.some((function(t){return r.indexOf(t)>-1&&(e.styleValidity(!0),e.adapter.setLabelRequired(e.getNativeInput().required),!0)})),t.indexOf("maxlength")>-1&&this.setcharacterCounter(this.getValue().length)},d.prototype.notchOutline=function(t){if(this.adapter.hasOutline()&&this.adapter.hasLabel())if(t){var e=this.adapter.getLabelWidth()*s.LABEL_SCALE;this.adapter.notchOutline(e)}else this.adapter.closeOutline()},d.prototype.activateFocus=function(){this.isFocused=!0,this.styleFocused(this.isFocused),this.adapter.activateLineRipple(),this.adapter.hasLabel()&&(this.notchOutline(this.shouldFloat),this.adapter.floatLabel(this.shouldFloat),this.styleFloating(this.shouldFloat),this.adapter.shakeLabel(this.shouldShake)),!this.helperText||!this.helperText.isPersistent()&&this.helperText.isValidation()&&this.valid||this.helperText.showToScreenReader()},d.prototype.setTransformOrigin=function(t){if(!this.isDisabled()&&!this.adapter.hasOutline()){var e=t.touches,i=e?e[0]:t,a=i.target.getBoundingClientRect(),n=i.clientX-a.left;this.adapter.setLineRippleTransformOrigin(n)}},d.prototype.handleInput=function(){this.autoCompleteFocus(),this.setcharacterCounter(this.getValue().length)},d.prototype.autoCompleteFocus=function(){this.receivedUserInput||this.activateFocus()},d.prototype.deactivateFocus=function(){this.isFocused=!1,this.adapter.deactivateLineRipple();var t=this.isValid();this.styleValidity(t),this.styleFocused(this.isFocused),this.adapter.hasLabel()&&(this.notchOutline(this.shouldFloat),this.adapter.floatLabel(this.shouldFloat),this.styleFloating(this.shouldFloat),this.adapter.shakeLabel(this.shouldShake)),this.shouldFloat||(this.receivedUserInput=!1)},d.prototype.getValue=function(){return this.getNativeInput().value},d.prototype.setValue=function(t){if(this.getValue()!==t&&(this.getNativeInput().value=t),this.setcharacterCounter(t.length),this.validateOnValueChange){var e=this.isValid();this.styleValidity(e)}this.adapter.hasLabel()&&(this.notchOutline(this.shouldFloat),this.adapter.floatLabel(this.shouldFloat),this.styleFloating(this.shouldFloat),this.validateOnValueChange&&this.adapter.shakeLabel(this.shouldShake))},d.prototype.isValid=function(){return this.useNativeValidation?this.isNativeInputValid():this.valid},d.prototype.setValid=function(t){this.valid=t,this.styleValidity(t);var e=!t&&!this.isFocused&&!!this.getValue();this.adapter.hasLabel()&&this.adapter.shakeLabel(e)},d.prototype.setValidateOnValueChange=function(t){this.validateOnValueChange=t},d.prototype.getValidateOnValueChange=function(){return this.validateOnValueChange},d.prototype.setUseNativeValidation=function(t){this.useNativeValidation=t},d.prototype.isDisabled=function(){return this.getNativeInput().disabled},d.prototype.setDisabled=function(t){this.getNativeInput().disabled=t,this.styleDisabled(t)},d.prototype.setHelperTextContent=function(t){this.helperText&&this.helperText.setContent(t)},d.prototype.setLeadingIconAriaLabel=function(t){this.leadingIcon&&this.leadingIcon.setAriaLabel(t)},d.prototype.setLeadingIconContent=function(t){this.leadingIcon&&this.leadingIcon.setContent(t)},d.prototype.setTrailingIconAriaLabel=function(t){this.trailingIcon&&this.trailingIcon.setAriaLabel(t)},d.prototype.setTrailingIconContent=function(t){this.trailingIcon&&this.trailingIcon.setContent(t)},d.prototype.setcharacterCounter=function(t){if(this.characterCounter){var e=this.getNativeInput().maxLength;if(-1===e)throw new Error("MDCTextFieldFoundation: Expected maxlength html property on text input or textarea.");this.characterCounter.setCounterValue(t,e)}},d.prototype.isBadInput=function(){return this.getNativeInput().validity.badInput||!1},d.prototype.isNativeInputValid=function(){return this.getNativeInput().validity.valid},d.prototype.styleValidity=function(t){var e=d.cssClasses.INVALID;if(t?this.adapter.removeClass(e):this.adapter.addClass(e),this.helperText){if(this.helperText.setValidity(t),!this.helperText.isValidation())return;var i=this.helperText.isVisible(),a=this.helperText.getId();i&&a?this.adapter.setInputAttr(o.ARIA_DESCRIBEDBY,a):this.adapter.removeInputAttr(o.ARIA_DESCRIBEDBY)}},d.prototype.styleFocused=function(t){var e=d.cssClasses.FOCUSED;t?this.adapter.addClass(e):this.adapter.removeClass(e)},d.prototype.styleDisabled=function(t){var e=d.cssClasses,i=e.DISABLED,a=e.INVALID;t?(this.adapter.addClass(i),this.adapter.removeClass(a)):this.adapter.removeClass(i),this.leadingIcon&&this.leadingIcon.setDisabled(t),this.trailingIcon&&this.trailingIcon.setDisabled(t)},d.prototype.styleFloating=function(t){var e=d.cssClasses.LABEL_FLOATING;t?this.adapter.addClass(e):this.adapter.removeClass(e)},d.prototype.getNativeInput=function(){return(this.adapter?this.adapter.getNativeInput():null)||{disabled:!1,maxLength:-1,required:!1,type:"input",validity:{badInput:!1,valid:!0},value:""}},d}(a);export{d as MDCTextFieldFoundation,d as default};
//# sourceMappingURL=foundation.js.map
