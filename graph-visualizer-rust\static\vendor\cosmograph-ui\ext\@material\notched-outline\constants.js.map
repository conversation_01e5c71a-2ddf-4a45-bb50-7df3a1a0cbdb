{"version": 3, "file": "constants.js", "sources": ["../../../../../../node_modules/@material/notched-outline/constants.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar strings = {\n    NOTCH_ELEMENT_SELECTOR: '.mdc-notched-outline__notch',\n};\nvar numbers = {\n    // This should stay in sync with $mdc-notched-outline-padding * 2.\n    NOTCH_ELEMENT_PADDING: 8,\n};\nvar cssClasses = {\n    NO_LABEL: 'mdc-notched-outline--no-label',\n    OUTLINE_NOTCHED: 'mdc-notched-outline--notched',\n    OUTLINE_UPGRADED: 'mdc-notched-outline--upgraded',\n};\nexport { cssClasses, numbers, strings };\n//# sourceMappingURL=constants.js.map"], "names": ["strings", "NOTCH_ELEMENT_SELECTOR", "numbers", "NOTCH_ELEMENT_PADDING", "cssClasses", "NO_LABEL", "OUTLINE_NOTCHED", "OUTLINE_UPGRADED"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAsBG,IAACA,EAAU,CACVC,uBAAwB,+BAExBC,EAAU,CAEVC,sBAAuB,GAEvBC,EAAa,CACbC,SAAU,gCACVC,gBAAiB,+BACjBC,iBAAkB"}