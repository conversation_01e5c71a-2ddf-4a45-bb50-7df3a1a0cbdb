{"version": 3, "file": "constants.js", "sources": ["../../../../../../node_modules/@material/list/constants.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar _a, _b;\nvar cssClasses = {\n    LIST_ITEM_ACTIVATED_CLASS: 'mdc-list-item--activated',\n    LIST_ITEM_CLASS: 'mdc-list-item',\n    LIST_ITEM_DISABLED_CLASS: 'mdc-list-item--disabled',\n    LIST_ITEM_SELECTED_CLASS: 'mdc-list-item--selected',\n    LIST_ITEM_TEXT_CLASS: 'mdc-list-item__text',\n    LIST_ITEM_PRIMARY_TEXT_CLASS: 'mdc-list-item__primary-text',\n    ROOT: 'mdc-list',\n};\nvar evolutionClassNameMap = (_a = {},\n    _a[\"\" + cssClasses.LIST_ITEM_ACTIVATED_CLASS] = 'mdc-list-item--activated',\n    _a[\"\" + cssClasses.LIST_ITEM_CLASS] = 'mdc-list-item',\n    _a[\"\" + cssClasses.LIST_ITEM_DISABLED_CLASS] = 'mdc-list-item--disabled',\n    _a[\"\" + cssClasses.LIST_ITEM_SELECTED_CLASS] = 'mdc-list-item--selected',\n    _a[\"\" + cssClasses.LIST_ITEM_PRIMARY_TEXT_CLASS] = 'mdc-list-item__primary-text',\n    _a[\"\" + cssClasses.ROOT] = 'mdc-list',\n    _a);\nvar deprecatedClassNameMap = (_b = {},\n    _b[\"\" + cssClasses.LIST_ITEM_ACTIVATED_CLASS] = 'mdc-deprecated-list-item--activated',\n    _b[\"\" + cssClasses.LIST_ITEM_CLASS] = 'mdc-deprecated-list-item',\n    _b[\"\" + cssClasses.LIST_ITEM_DISABLED_CLASS] = 'mdc-deprecated-list-item--disabled',\n    _b[\"\" + cssClasses.LIST_ITEM_SELECTED_CLASS] = 'mdc-deprecated-list-item--selected',\n    _b[\"\" + cssClasses.LIST_ITEM_TEXT_CLASS] = 'mdc-deprecated-list-item__text',\n    _b[\"\" + cssClasses.LIST_ITEM_PRIMARY_TEXT_CLASS] = 'mdc-deprecated-list-item__primary-text',\n    _b[\"\" + cssClasses.ROOT] = 'mdc-deprecated-list',\n    _b);\nvar strings = {\n    ACTION_EVENT: 'MDCList:action',\n    SELECTION_CHANGE_EVENT: 'MDCList:selectionChange',\n    ARIA_CHECKED: 'aria-checked',\n    ARIA_CHECKED_CHECKBOX_SELECTOR: '[role=\"checkbox\"][aria-checked=\"true\"]',\n    ARIA_CHECKED_RADIO_SELECTOR: '[role=\"radio\"][aria-checked=\"true\"]',\n    ARIA_CURRENT: 'aria-current',\n    ARIA_DISABLED: 'aria-disabled',\n    ARIA_ORIENTATION: 'aria-orientation',\n    ARIA_ORIENTATION_HORIZONTAL: 'horizontal',\n    ARIA_ROLE_CHECKBOX_SELECTOR: '[role=\"checkbox\"]',\n    ARIA_SELECTED: 'aria-selected',\n    ARIA_INTERACTIVE_ROLES_SELECTOR: '[role=\"listbox\"], [role=\"menu\"]',\n    ARIA_MULTI_SELECTABLE_SELECTOR: '[aria-multiselectable=\"true\"]',\n    CHECKBOX_RADIO_SELECTOR: 'input[type=\"checkbox\"], input[type=\"radio\"]',\n    CHECKBOX_SELECTOR: 'input[type=\"checkbox\"]',\n    CHILD_ELEMENTS_TO_TOGGLE_TABINDEX: \"\\n    .\" + cssClasses.LIST_ITEM_CLASS + \" button:not(:disabled),\\n    .\" + cssClasses.LIST_ITEM_CLASS + \" a,\\n    .\" + deprecatedClassNameMap[cssClasses.LIST_ITEM_CLASS] + \" button:not(:disabled),\\n    .\" + deprecatedClassNameMap[cssClasses.LIST_ITEM_CLASS] + \" a\\n  \",\n    DEPRECATED_SELECTOR: '.mdc-deprecated-list',\n    FOCUSABLE_CHILD_ELEMENTS: \"\\n    .\" + cssClasses.LIST_ITEM_CLASS + \" button:not(:disabled),\\n    .\" + cssClasses.LIST_ITEM_CLASS + \" a,\\n    .\" + cssClasses.LIST_ITEM_CLASS + \" input[type=\\\"radio\\\"]:not(:disabled),\\n    .\" + cssClasses.LIST_ITEM_CLASS + \" input[type=\\\"checkbox\\\"]:not(:disabled),\\n    .\" + deprecatedClassNameMap[cssClasses.LIST_ITEM_CLASS] + \" button:not(:disabled),\\n    .\" + deprecatedClassNameMap[cssClasses.LIST_ITEM_CLASS] + \" a,\\n    .\" + deprecatedClassNameMap[cssClasses.LIST_ITEM_CLASS] + \" input[type=\\\"radio\\\"]:not(:disabled),\\n    .\" + deprecatedClassNameMap[cssClasses.LIST_ITEM_CLASS] + \" input[type=\\\"checkbox\\\"]:not(:disabled)\\n  \",\n    RADIO_SELECTOR: 'input[type=\"radio\"]',\n    SELECTED_ITEM_SELECTOR: '[aria-selected=\"true\"], [aria-current=\"true\"]',\n};\nvar numbers = {\n    UNSET_INDEX: -1,\n    TYPEAHEAD_BUFFER_CLEAR_TIMEOUT_MS: 300\n};\nvar evolutionAttribute = 'evolution';\nexport { strings, cssClasses, numbers, deprecatedClassNameMap, evolutionAttribute, evolutionClassNameMap };\n//# sourceMappingURL=constants.js.map"], "names": ["_a", "_b", "cssClasses", "LIST_ITEM_ACTIVATED_CLASS", "LIST_ITEM_CLASS", "LIST_ITEM_DISABLED_CLASS", "LIST_ITEM_SELECTED_CLASS", "LIST_ITEM_TEXT_CLASS", "LIST_ITEM_PRIMARY_TEXT_CLASS", "ROOT", "deprecatedClassNameMap", "strings", "ACTION_EVENT", "SELECTION_CHANGE_EVENT", "ARIA_CHECKED", "ARIA_CHECKED_CHECKBOX_SELECTOR", "ARIA_CHECKED_RADIO_SELECTOR", "ARIA_CURRENT", "ARIA_DISABLED", "ARIA_ORIENTATION", "ARIA_ORIENTATION_HORIZONTAL", "ARIA_ROLE_CHECKBOX_SELECTOR", "ARIA_SELECTED", "ARIA_INTERACTIVE_ROLES_SELECTOR", "ARIA_MULTI_SELECTABLE_SELECTOR", "CHECKBOX_RADIO_SELECTOR", "CHECKBOX_SELECTOR", "CHILD_ELEMENTS_TO_TOGGLE_TABINDEX", "DEPRECATED_SELECTOR", "FOCUSABLE_CHILD_ELEMENTS", "RADIO_SELECTOR", "SELECTED_ITEM_SELECTOR", "numbers", "UNSET_INDEX", "TYPEAHEAD_BUFFER_CLEAR_TIMEOUT_MS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAsBA,IAAIA,EAAIC,EACJC,EAAa,CACbC,0BAA2B,2BAC3BC,gBAAiB,gBACjBC,yBAA0B,0BAC1BC,yBAA0B,0BAC1BC,qBAAsB,sBACtBC,6BAA8B,8BAC9BC,KAAM,aAEmBT,EAAK,CAAE,GAC7B,GAAKE,EAAWC,2BAA6B,2BAChDH,EAAG,GAAKE,EAAWE,iBAAmB,gBACtCJ,EAAG,GAAKE,EAAWG,0BAA4B,0BAC/CL,EAAG,GAAKE,EAAWI,0BAA4B,0BAC/CN,EAAG,GAAKE,EAAWM,8BAAgC,8BACnDR,EAAG,GAAKE,EAAWO,MAAQ,WAE5B,IAACC,IAA0BT,EAAK,CAAE,GAC9B,GAAKC,EAAWC,2BAA6B,sCAChDF,EAAG,GAAKC,EAAWE,iBAAmB,2BACtCH,EAAG,GAAKC,EAAWG,0BAA4B,qCAC/CJ,EAAG,GAAKC,EAAWI,0BAA4B,qCAC/CL,EAAG,GAAKC,EAAWK,sBAAwB,iCAC3CN,EAAG,GAAKC,EAAWM,8BAAgC,yCACnDP,EAAG,GAAKC,EAAWO,MAAQ,sBAC3BR,GACAU,EAAU,CACVC,aAAc,iBACdC,uBAAwB,0BACxBC,aAAc,eACdC,+BAAgC,yCAChCC,4BAA6B,sCAC7BC,aAAc,eACdC,cAAe,gBACfC,iBAAkB,mBAClBC,4BAA6B,aAC7BC,4BAA6B,oBAC7BC,cAAe,gBACfC,gCAAiC,kCACjCC,+BAAgC,gCAChCC,wBAAyB,8CACzBC,kBAAmB,yBACnBC,kCAAmC,UAAYzB,EAAWE,gBAAkB,iCAAmCF,EAAWE,gBAAkB,aAAeM,EAAuBR,EAAWE,iBAAmB,iCAAmCM,EAAuBR,EAAWE,iBAAmB,SACxSwB,oBAAqB,uBACrBC,yBAA0B,UAAY3B,EAAWE,gBAAkB,iCAAmCF,EAAWE,gBAAkB,aAAeF,EAAWE,gBAAkB,8CAAkDF,EAAWE,gBAAkB,iDAAqDM,EAAuBR,EAAWE,iBAAmB,iCAAmCM,EAAuBR,EAAWE,iBAAmB,aAAeM,EAAuBR,EAAWE,iBAAmB,8CAAkDM,EAAuBR,EAAWE,iBAAmB,6CAC3mB0B,eAAgB,sBAChBC,uBAAwB,iDAExBC,EAAU,CACVC,aAAc,EACdC,kCAAmC"}