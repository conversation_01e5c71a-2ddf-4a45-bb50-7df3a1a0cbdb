import{__extends as t,__assign as i,__values as e}from"../../../_virtual/_tslib.js";import{MDCFoundation as n}from"../base/foundation.js";import{Corner as s,cssClasses as o,strings as r,numbers as a,CornerBit as h}from"./constants.js";
/**
 * @license
 * Copyright 2018 Google Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */var c=function(n){function c(t){var e=n.call(this,i(i({},c.defaultAdapter),t))||this;return e.isSurfaceOpen=!1,e.isQuickOpen=!1,e.isHoistedElement=!1,e.isFixedPosition=!1,e.isHorizontallyCenteredOnViewport=!1,e.maxHeight=0,e.openBottomBias=0,e.openAnimationEndTimerId=0,e.closeAnimationEndTimerId=0,e.animationRequestId=0,e.anchorCorner=s.TOP_START,e.originCorner=s.TOP_START,e.anchorMargin={top:0,right:0,bottom:0,left:0},e.position={x:0,y:0},e}return t(c,n),Object.defineProperty(c,"cssClasses",{get:function(){return o},enumerable:!1,configurable:!0}),Object.defineProperty(c,"strings",{get:function(){return r},enumerable:!1,configurable:!0}),Object.defineProperty(c,"numbers",{get:function(){return a},enumerable:!1,configurable:!0}),Object.defineProperty(c,"Corner",{get:function(){return s},enumerable:!1,configurable:!0}),Object.defineProperty(c,"defaultAdapter",{get:function(){return{addClass:function(){},removeClass:function(){},hasClass:function(){return!1},hasAnchor:function(){return!1},isElementInContainer:function(){return!1},isFocused:function(){return!1},isRtl:function(){return!1},getInnerDimensions:function(){return{height:0,width:0}},getAnchorDimensions:function(){return null},getWindowDimensions:function(){return{height:0,width:0}},getBodyDimensions:function(){return{height:0,width:0}},getWindowScroll:function(){return{x:0,y:0}},setPosition:function(){},setMaxHeight:function(){},setTransformOrigin:function(){},saveFocus:function(){},restoreFocus:function(){},notifyClose:function(){},notifyClosing:function(){},notifyOpen:function(){},notifyOpening:function(){}}},enumerable:!1,configurable:!0}),c.prototype.init=function(){var t=c.cssClasses,i=t.ROOT,e=t.OPEN;if(!this.adapter.hasClass(i))throw new Error(i+" class required in root element.");this.adapter.hasClass(e)&&(this.isSurfaceOpen=!0)},c.prototype.destroy=function(){clearTimeout(this.openAnimationEndTimerId),clearTimeout(this.closeAnimationEndTimerId),cancelAnimationFrame(this.animationRequestId)},c.prototype.setAnchorCorner=function(t){this.anchorCorner=t},c.prototype.flipCornerHorizontally=function(){this.originCorner=this.originCorner^h.RIGHT},c.prototype.setAnchorMargin=function(t){this.anchorMargin.top=t.top||0,this.anchorMargin.right=t.right||0,this.anchorMargin.bottom=t.bottom||0,this.anchorMargin.left=t.left||0},c.prototype.setIsHoisted=function(t){this.isHoistedElement=t},c.prototype.setFixedPosition=function(t){this.isFixedPosition=t},c.prototype.isFixed=function(){return this.isFixedPosition},c.prototype.setAbsolutePosition=function(t,i){this.position.x=this.isFinite(t)?t:0,this.position.y=this.isFinite(i)?i:0},c.prototype.setIsHorizontallyCenteredOnViewport=function(t){this.isHorizontallyCenteredOnViewport=t},c.prototype.setQuickOpen=function(t){this.isQuickOpen=t},c.prototype.setMaxHeight=function(t){this.maxHeight=t},c.prototype.setOpenBottomBias=function(t){this.openBottomBias=t},c.prototype.isOpen=function(){return this.isSurfaceOpen},c.prototype.open=function(){var t=this;this.isSurfaceOpen||(this.adapter.notifyOpening(),this.adapter.saveFocus(),this.isQuickOpen?(this.isSurfaceOpen=!0,this.adapter.addClass(c.cssClasses.OPEN),this.dimensions=this.adapter.getInnerDimensions(),this.autoposition(),this.adapter.notifyOpen()):(this.adapter.addClass(c.cssClasses.ANIMATING_OPEN),this.animationRequestId=requestAnimationFrame((function(){t.dimensions=t.adapter.getInnerDimensions(),t.autoposition(),t.adapter.addClass(c.cssClasses.OPEN),t.openAnimationEndTimerId=setTimeout((function(){t.openAnimationEndTimerId=0,t.adapter.removeClass(c.cssClasses.ANIMATING_OPEN),t.adapter.notifyOpen()}),a.TRANSITION_OPEN_DURATION)})),this.isSurfaceOpen=!0))},c.prototype.close=function(t){var i=this;if(void 0===t&&(t=!1),this.isSurfaceOpen){if(this.adapter.notifyClosing(),this.isQuickOpen)return this.isSurfaceOpen=!1,t||this.maybeRestoreFocus(),this.adapter.removeClass(c.cssClasses.OPEN),this.adapter.removeClass(c.cssClasses.IS_OPEN_BELOW),void this.adapter.notifyClose();this.adapter.addClass(c.cssClasses.ANIMATING_CLOSED),requestAnimationFrame((function(){i.adapter.removeClass(c.cssClasses.OPEN),i.adapter.removeClass(c.cssClasses.IS_OPEN_BELOW),i.closeAnimationEndTimerId=setTimeout((function(){i.closeAnimationEndTimerId=0,i.adapter.removeClass(c.cssClasses.ANIMATING_CLOSED),i.adapter.notifyClose()}),a.TRANSITION_CLOSE_DURATION)})),this.isSurfaceOpen=!1,t||this.maybeRestoreFocus()}},c.prototype.handleBodyClick=function(t){var i=t.target;this.adapter.isElementInContainer(i)||this.close()},c.prototype.handleKeydown=function(t){var i=t.keyCode;("Escape"===t.key||27===i)&&this.close()},c.prototype.autoposition=function(){var t;this.measurements=this.getAutoLayoutmeasurements();var i=this.getoriginCorner(),e=this.getMenuSurfaceMaxHeight(i),n=this.hasBit(i,h.BOTTOM)?"bottom":"top",s=this.hasBit(i,h.RIGHT)?"right":"left",o=this.getHorizontalOriginOffset(i),r=this.getVerticalOriginOffset(i),u=this.measurements,p=u.anchorSize,d=u.surfaceSize,f=((t={})[s]=o,t[n]=r,t);p.width/d.width>a.ANCHOR_TO_MENU_SURFACE_WIDTH_RATIO&&(s="center"),(this.isHoistedElement||this.isFixedPosition)&&this.adjustPositionForHoistedElement(f),this.adapter.setTransformOrigin(s+" "+n),this.adapter.setPosition(f),this.adapter.setMaxHeight(e?e+"px":""),this.hasBit(i,h.BOTTOM)||this.adapter.addClass(c.cssClasses.IS_OPEN_BELOW)},c.prototype.getAutoLayoutmeasurements=function(){var t=this.adapter.getAnchorDimensions(),i=this.adapter.getBodyDimensions(),e=this.adapter.getWindowDimensions(),n=this.adapter.getWindowScroll();return t||(t={top:this.position.y,right:this.position.x,bottom:this.position.y,left:this.position.x,width:0,height:0}),{anchorSize:t,bodySize:i,surfaceSize:this.dimensions,viewportDistance:{top:t.top,right:e.width-t.right,bottom:e.height-t.bottom,left:t.left},viewportSize:e,windowScroll:n}},c.prototype.getoriginCorner=function(){var t,i,e=this.originCorner,n=this.measurements,s=n.viewportDistance,o=n.anchorSize,r=n.surfaceSize,a=c.numbers.MARGIN_TO_EDGE;this.hasBit(this.anchorCorner,h.BOTTOM)?(t=s.top-a+this.anchorMargin.bottom,i=s.bottom-a-this.anchorMargin.bottom):(t=s.top-a+this.anchorMargin.top,i=s.bottom-a+o.height-this.anchorMargin.top),!(i-r.height>0)&&t>i+this.openBottomBias&&(e=this.setBit(e,h.BOTTOM));var u,p,d=this.adapter.isRtl(),f=this.hasBit(this.anchorCorner,h.FLIP_RTL),m=this.hasBit(this.anchorCorner,h.RIGHT)||this.hasBit(e,h.RIGHT),l=!1;(l=d&&f?!m:m)?(u=s.left+o.width+this.anchorMargin.right,p=s.right-this.anchorMargin.right):(u=s.left+this.anchorMargin.left,p=s.right+o.width-this.anchorMargin.left);var g=u-r.width>0,O=p-r.width>0,y=this.hasBit(e,h.FLIP_RTL)&&this.hasBit(e,h.RIGHT);return O&&y&&d||!g&&y?e=this.unsetBit(e,h.RIGHT):(g&&l&&d||g&&!l&&m||!O&&u>=p)&&(e=this.setBit(e,h.RIGHT)),e},c.prototype.getMenuSurfaceMaxHeight=function(t){if(this.maxHeight>0)return this.maxHeight;var i=this.measurements.viewportDistance,e=0,n=this.hasBit(t,h.BOTTOM),s=this.hasBit(this.anchorCorner,h.BOTTOM),o=c.numbers.MARGIN_TO_EDGE;return n?(e=i.top+this.anchorMargin.top-o,s||(e+=this.measurements.anchorSize.height)):(e=i.bottom-this.anchorMargin.bottom+this.measurements.anchorSize.height-o,s&&(e-=this.measurements.anchorSize.height)),e},c.prototype.getHorizontalOriginOffset=function(t){var i=this.measurements.anchorSize,e=this.hasBit(t,h.RIGHT),n=this.hasBit(this.anchorCorner,h.RIGHT);if(e){var s=n?i.width-this.anchorMargin.left:this.anchorMargin.right;return this.isHoistedElement||this.isFixedPosition?s-(this.measurements.viewportSize.width-this.measurements.bodySize.width):s}return n?i.width-this.anchorMargin.right:this.anchorMargin.left},c.prototype.getVerticalOriginOffset=function(t){var i=this.measurements.anchorSize,e=this.hasBit(t,h.BOTTOM),n=this.hasBit(this.anchorCorner,h.BOTTOM);return e?n?i.height-this.anchorMargin.top:-this.anchorMargin.bottom:n?i.height+this.anchorMargin.bottom:this.anchorMargin.top},c.prototype.adjustPositionForHoistedElement=function(t){var i,n,s=this.measurements,o=s.windowScroll,r=s.viewportDistance,a=s.surfaceSize,h=s.viewportSize,c=Object.keys(t);try{for(var u=e(c),p=u.next();!p.done;p=u.next()){var d=p.value,f=t[d]||0;!this.isHorizontallyCenteredOnViewport||"left"!==d&&"right"!==d?(f+=r[d],this.isFixedPosition||("top"===d?f+=o.y:"bottom"===d?f-=o.y:"left"===d?f+=o.x:f-=o.x),t[d]=f):t[d]=(h.width-a.width)/2}}catch(t){i={error:t}}finally{try{p&&!p.done&&(n=u.return)&&n.call(u)}finally{if(i)throw i.error}}},c.prototype.maybeRestoreFocus=function(){var t=this,i=this.adapter.isFocused(),e=this.adapter.getOwnerDocument?this.adapter.getOwnerDocument():document,n=e.activeElement&&this.adapter.isElementInContainer(e.activeElement);(i||n)&&setTimeout((function(){t.adapter.restoreFocus()}),a.TOUCH_EVENT_WAIT_MS)},c.prototype.hasBit=function(t,i){return Boolean(t&i)},c.prototype.setBit=function(t,i){return t|i},c.prototype.unsetBit=function(t,i){return t^i},c.prototype.isFinite=function(t){return"number"==typeof t&&isFinite(t)},c}(n);export{c as MDCMenuSurfaceFoundation,c as default};
//# sourceMappingURL=foundation.js.map
