{"version": 3, "file": "constants.js", "sources": ["../../../../../../node_modules/@material/textfield/constants.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar strings = {\n    ARIA_CONTROLS: 'aria-controls',\n    ARIA_DESCRIBEDBY: 'aria-describedby',\n    INPUT_SELECTOR: '.mdc-text-field__input',\n    LABEL_SELECTOR: '.mdc-floating-label',\n    LEADING_ICON_SELECTOR: '.mdc-text-field__icon--leading',\n    LINE_RIPPLE_SELECTOR: '.mdc-line-ripple',\n    OUTLINE_SELECTOR: '.mdc-notched-outline',\n    PREFIX_SELECTOR: '.mdc-text-field__affix--prefix',\n    SUFFIX_SELECTOR: '.mdc-text-field__affix--suffix',\n    TRAILING_ICON_SELECTOR: '.mdc-text-field__icon--trailing'\n};\nvar cssClasses = {\n    DISABLED: 'mdc-text-field--disabled',\n    FOCUSED: 'mdc-text-field--focused',\n    HELPER_LINE: 'mdc-text-field-helper-line',\n    INVALID: 'mdc-text-field--invalid',\n    LABEL_FLOATING: 'mdc-text-field--label-floating',\n    NO_LABEL: 'mdc-text-field--no-label',\n    OUTLINED: 'mdc-text-field--outlined',\n    ROOT: 'mdc-text-field',\n    TEXTAREA: 'mdc-text-field--textarea',\n    WITH_LEADING_ICON: 'mdc-text-field--with-leading-icon',\n    WITH_TRAILING_ICON: 'mdc-text-field--with-trailing-icon',\n    WITH_INTERNAL_COUNTER: 'mdc-text-field--with-internal-counter',\n};\nvar numbers = {\n    LABEL_SCALE: 0.75,\n};\n/**\n * Whitelist based off of\n * https://developer.mozilla.org/en-US/docs/Web/Guide/HTML/HTML5/Constraint_validation\n * under the \"Validation-related attributes\" section.\n */\nvar VALIDATION_ATTR_WHITELIST = [\n    'pattern',\n    'min',\n    'max',\n    'required',\n    'step',\n    'minlength',\n    'maxlength',\n];\n/**\n * Label should always float for these types as they show some UI even if value\n * is empty.\n */\nvar ALWAYS_FLOAT_TYPES = [\n    'color',\n    'date',\n    'datetime-local',\n    'month',\n    'range',\n    'time',\n    'week',\n];\nexport { cssClasses, strings, numbers, VALIDATION_ATTR_WHITELIST, ALWAYS_FLOAT_TYPES };\n//# sourceMappingURL=constants.js.map"], "names": ["strings", "ARIA_CONTROLS", "ARIA_DESCRIBEDBY", "INPUT_SELECTOR", "LABEL_SELECTOR", "LEADING_ICON_SELECTOR", "LINE_RIPPLE_SELECTOR", "OUTLINE_SELECTOR", "PREFIX_SELECTOR", "SUFFIX_SELECTOR", "TRAILING_ICON_SELECTOR", "cssClasses", "DISABLED", "FOCUSED", "HELPER_LINE", "INVALID", "LABEL_FLOATING", "NO_LABEL", "OUTLINED", "ROOT", "TEXTAREA", "WITH_LEADING_ICON", "WITH_TRAILING_ICON", "WITH_INTERNAL_COUNTER", "numbers", "LABEL_SCALE", "VALIDATION_ATTR_WHITELIST", "ALWAYS_FLOAT_TYPES"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAsBG,IAACA,EAAU,CACVC,cAAe,gBACfC,iBAAkB,mBAClBC,eAAgB,yBAChBC,eAAgB,sBAChBC,sBAAuB,iCACvBC,qBAAsB,mBACtBC,iBAAkB,uBAClBC,gBAAiB,iCACjBC,gBAAiB,iCACjBC,uBAAwB,mCAExBC,EAAa,CACbC,SAAU,2BACVC,QAAS,0BACTC,YAAa,6BACbC,QAAS,0BACTC,eAAgB,iCAChBC,SAAU,2BACVC,SAAU,2BACVC,KAAM,iBACNC,SAAU,2BACVC,kBAAmB,oCACnBC,mBAAoB,qCACpBC,sBAAuB,yCAEvBC,EAAU,CACVC,YAAa,KAObC,EAA4B,CAC5B,UACA,MACA,MACA,WACA,OACA,YACA,aAMAC,EAAqB,CACrB,QACA,OACA,iBACA,QACA,QACA,OACA"}