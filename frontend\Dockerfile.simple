# Multi-stage build for production-ready React frontend (simplified)
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies - skip postinstall scripts that fail
RUN npm install --force --ignore-scripts && \
    npm rebuild --ignore-scripts || true

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage with simple serve
FROM node:18-alpine AS production

# Install serve globally
RUN npm install -g serve

# Set working directory
WORKDIR /app

# Copy built assets from builder stage
COPY --from=builder /app/dist ./dist

# Create health endpoint
RUN echo '<!DOCTYPE html><html><head><title>Health Check</title></head><body><h1>OK</h1></body></html>' > dist/health

# Expose port
EXPOSE 3000

# Start serve
CMD ["serve", "-s", "dist", "-l", "3000"]