{"version": 3, "file": "constants.js", "sources": ["../../../../../../node_modules/@material/floating-label/constants.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nexport var cssClasses = {\n    LABEL_FLOAT_ABOVE: 'mdc-floating-label--float-above',\n    LABEL_REQUIRED: 'mdc-floating-label--required',\n    LABEL_SHAKE: 'mdc-floating-label--shake',\n    ROOT: 'mdc-floating-label',\n};\n//# sourceMappingURL=constants.js.map"], "names": ["cssClasses", "LABEL_FLOAT_ABOVE", "LABEL_REQUIRED", "LABEL_SHAKE", "ROOT"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAsBU,IAACA,EAAa,CACpBC,kBAAmB,kCACnBC,eAAgB,+BAChBC,YAAa,4BACbC,KAAM"}