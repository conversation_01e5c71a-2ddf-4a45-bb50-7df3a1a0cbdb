{"extends": ["plugin:@typescript-eslint/recommended", "standard"], "env": {"es6": true, "node": true, "browser": true}, "plugins": ["import", "unicorn"], "parser": "@typescript-eslint/parser", "rules": {"object-curly-spacing": ["error", "always"], "prefer-template": "error", "no-useless-constructor": "off", "import/extensions": 0, "import/no-extraneous-dependencies": 0, "import/no-default-export": "error", "import/prefer-default-export": 0, "import/first": ["error", "absolute-first"], "import/order": ["error", {"groups": ["builtin", "external", "parent", "sibling", "index", "object"]}], "comma-dangle": ["error", {"arrays": "always-multiline", "objects": "always-multiline", "imports": "always-multiline", "exports": "always-multiline", "functions": "never"}], "indent": ["error", 2], "consistent-return": "error", "no-console": ["error", {"allow": ["warn", "error"]}], "no-undef": 0, "no-unused-expressions": "off", "no-multiple-empty-lines": 2, "no-use-before-define": "off", "@typescript-eslint/no-use-before-define": ["error", {"variables": false}], "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": ["error"], "@typescript-eslint/no-unused-expressions": ["error"], "@typescript-eslint/no-explicit-any": ["error"], "@typescript-eslint/indent": "off", "@typescript-eslint/type-annotation-spacing": ["error"], "@typescript-eslint/member-delimiter-style": ["error", {"multiline": {"delimiter": "semi", "requireLast": true}, "singleline": {"delimiter": "semi", "requireLast": false}}], "@typescript-eslint/member-ordering": "error", "@typescript-eslint/explicit-member-accessibility": "error", "@typescript-eslint/explicit-function-return-type": "error", "max-len": ["warn", {"code": 160, "comments": 160}], "unicorn/filename-case": "error", "unicorn/no-for-loop": "error", "unicorn/prefer-array-some": "error", "unicorn/no-instanceof-array": "error", "unicorn/prefer-includes": "error", "unicorn/prefer-keyboard-event-key": "error"}}