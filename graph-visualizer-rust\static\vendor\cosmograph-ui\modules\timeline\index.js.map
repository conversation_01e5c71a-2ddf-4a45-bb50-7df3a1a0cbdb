{"version": 3, "file": "index.js", "sources": ["../../../src/modules/timeline/index.ts"], "sourcesContent": ["import 'd3-transition'\nimport { ResizeObserver } from '@juggle/resize-observer'\nimport { axisBottom } from 'd3-axis'\nimport { brushX, type BrushBehavior, type BrushSelection } from 'd3-brush'\nimport { scaleTime, scaleSymlog, scaleLinear, type ScaleLinear, type ScaleTime } from 'd3-scale'\nimport { select, type Selection } from 'd3-selection'\nimport { extent, rollup, range, pairs } from 'd3-array'\nimport { play, pause } from './assets'\nimport { TimelineConfig, type TimelineEvents, type TimelineConfigInterface } from './config'\n\nimport type { BarData, TimelineData } from './types'\n\nimport s from './style.module.css'\nimport { getCountsInRange, getInnerDimensions } from '../../utils'\n\nexport class Timeline {\n  private _animationInterval: ReturnType<typeof setTimeout> | undefined\n  private _isAnimationRunning = false\n  private _svgParser = new DOMParser()\n\n  private _svg: SVGElement\n  private _animationControlDiv: HTMLElement\n  private _noDataDiv: HTMLElement\n  private _playButtonSvg: SVGElement | undefined\n  private _pauseButtonSvg: SVGElement | undefined\n  private _containerNode: HTMLElement\n  private _resizeObserver: ResizeObserver\n  private _axisGroup: Selection<SVGGElement, unknown, null, undefined>\n  private _barsGroup: Selection<SVGGElement, unknown, null, undefined>\n  private _brushGroup: Selection<SVGGElement, unknown, null, undefined>\n\n  private _height = 0\n  private _width = 0\n  private _timelineWidth = 0\n  private _timelineHeight = 0\n\n  private _config = new TimelineConfig()\n\n  private _barWidth = 0\n  private _maxCount = 0\n\n  private _barsData: BarData[] = []\n  private _timeData: TimelineData = undefined\n  private _dateExtent: [Date, Date] | [number, number] | undefined\n  private _bandIntervals: (number | Date)[] = []\n  private _currentSelection: [Date, Date] | [number, number] | undefined\n  private _currentSelectionInPixels: [number, number] | undefined\n  private _isNumericTimeline = false\n  private _firstRender = true\n\n  private _yScale = scaleSymlog<number, number, never>()\n  private _timeScale = scaleTime<number, number, never>()\n  private _numScale = scaleLinear<number, number, never>()\n  private _activeAxisScale: ScaleTime<number, number, never> | ScaleLinear<number, number, never> = this._timeScale\n\n  private _timeAxis = axisBottom<Date>(this._timeScale)\n  private _numAxis = axisBottom<number>(this._numScale)\n\n  private _brushInstance: BrushBehavior<unknown> | undefined\n\n  public constructor (containerNode: HTMLElement, config?: TimelineConfigInterface) {\n    if (config) this._config.init(config)\n    this._containerNode = containerNode\n\n    this._svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')\n    this._svg.classList.add(s.timelineSvg)\n    this._animationControlDiv = document.createElement('div')\n    this._animationControlDiv.classList.add(s.animationControl)\n\n    this._containerNode.classList.add(s.timeline)\n    this._containerNode.appendChild(this._svg)\n\n    this._noDataDiv = document.createElement('div')\n    select(this._noDataDiv)\n      .style('display', 'none')\n      .attr('class', s.noData)\n      .append('div')\n      .text('No timeline data')\n    this._containerNode.appendChild(this._noDataDiv)\n\n    if (this._config?.showAnimationControls) {\n      const elementExist = setInterval(() => {\n        if (this._containerNode !== null) {\n          this._initAnimationControls()\n          clearInterval(elementExist)\n        }\n      }, 100)\n    }\n\n    this._barsGroup = select(this._svg).append('g').attr('class', s.bars)\n    this._axisGroup = select(this._svg).append('g').attr('class', s.axis)\n    this._brushGroup = select(this._svg).append('g').attr('class', s.brush)\n\n    this._timeAxis.tickFormat(this._config.formatter)\n    this._numAxis.tickFormat(this._config.formatter)\n\n    this._resizeObserver = new ResizeObserver((entries) => {\n      // We wrap it in requestAnimationFrame to avoid this error - ResizeObserver loop limit exceeded https://stackoverflow.com/a/58701523\n      window.requestAnimationFrame(() => {\n        if (!Array.isArray(entries) || !entries.length) {\n          return\n        }\n        this.resize()\n      })\n    })\n    this._resizeObserver = new ResizeObserver(() => { this.resize() })\n    this._resizeObserver.observe(this._containerNode)\n  }\n\n  private get _barPadding (): number {\n    return this._barWidth * this._config.barPadding\n  }\n\n  /**  `getCurrentSelection`: Returns current brush selection in data units (`Date` or `number`). */\n  public getCurrentSelection (): [Date, Date] | [number, number] | undefined {\n    return this._currentSelection\n  }\n\n  /**  `getCurrentSelectionInPixels`: Returns current brush selection in pixels. */\n  public getCurrentSelectionInPixels (): [number, number] | undefined {\n    return this._currentSelectionInPixels\n  }\n\n  /**  `getBarWidth`: Returns computed bar width in pixels */\n  public getBarWidth (): number {\n    return this._barWidth - this._barPadding\n  }\n\n  /**  `getConfig`: Returns current `Timeline` configuration */\n  public getConfig (): TimelineConfig {\n    return this._config\n  }\n\n  /**  `getIsAnimationRunning`: Returns a boolean value indicating if the animation is running. */\n  public getIsAnimationRunning (): boolean {\n    return this._isAnimationRunning\n  }\n\n  /**  `setConfig`: Function for setting config of `Timeline`. */\n  public setConfig (config?: TimelineConfigInterface): void {\n    const prevConfig = JSON.parse(JSON.stringify(this._config))\n    if (!config) {\n      this._config = new TimelineConfig()\n    } else {\n      this._config.init(config)\n    }\n    if (this._config?.showAnimationControls) {\n      if (!this._animationControlDiv?.isConnected) this._initAnimationControls()\n    } else if (this._animationControlDiv) {\n      this._disableAnimation()\n    }\n    if (!this._config.allowSelection) {\n      this._disableBrush()\n    }\n    if (this._config.formatter) {\n      this._timeAxis.tickFormat(this._config.formatter)\n      this._numAxis.tickFormat(this._config.formatter)\n    }\n    // Data-related props\n    if (this._config?.dataStep !== prevConfig.config?.dataStep || this._config?.barCount !== prevConfig.config?.barCount) {\n      this._updateTimelineData()\n    }\n    this.resize()\n  }\n\n  /**  `setTimeData`: Function for setting data of `Timeline`. */\n  public setTimeData (timeData: TimelineData): void {\n    this._timeData = timeData?.filter(d => !isNaN(+d) && d !== undefined)\n    this._currentSelection = undefined\n    this._config.events.onBrush?.(this._currentSelection)\n    this._updateScales()\n    select(this._noDataDiv).style('display', 'none')\n    if (this._timeData?.length) {\n      this._dateExtent = extent(this._timeData) as [number, number] | [Date, Date]\n      this._updateTimelineData()\n    } else {\n      this._barsData = []\n      this._axisGroup.selectAll('*').remove()\n      this._barsGroup.selectAll('*').remove()\n      this._brushGroup.selectAll('*').remove()\n      select(this._noDataDiv).style('display', 'block')\n      this._firstRender = true\n    }\n  }\n\n  private _getBarsData (rangeForIntervals: [number, number], date?: boolean): void {\n    if (rangeForIntervals[1] <= rangeForIntervals[0]) return\n    if (this._timeData?.length && this._dateExtent) {\n      const countedDates = rollup(this._timeData, v => v.length, d => d)\n      const step = this._config.dataStep ?? (rangeForIntervals[1] - rangeForIntervals[0]) / (this._config.barCount - 1)\n      if (step === 0) return\n      this._bandIntervals = range(+rangeForIntervals[0], +rangeForIntervals[1], step)\n      const lastTick = this._bandIntervals[this._bandIntervals.length - 1] as number\n      let lastInterval: Date | number = this._config.dataStep ? +lastTick + step : rangeForIntervals[1]\n      if (date) {\n        this._bandIntervals = this._bandIntervals.map(d => new Date(d))\n        lastInterval = new Date(lastInterval)\n      }\n      if (lastTick < rangeForIntervals[1]) this._bandIntervals.push(lastInterval)\n      const datePairs = pairs(this._bandIntervals)\n      this._barsData = datePairs.map(d => ({\n        rangeStart: d[0],\n        rangeEnd: d[1],\n        count: getCountsInRange(countedDates, d),\n      }))\n    }\n  }\n\n  private _updateTimelineData (): void {\n    if (this._timeData?.length && this._dateExtent) {\n      this._isNumericTimeline = !(this._timeData[0] instanceof Date)\n      if (this._isNumericTimeline) {\n        this._getBarsData(this._dateExtent as [number, number])\n      } else {\n        this._timeData = this._timeData.map(d => new Date(d)) as [Date, Date]\n        const extent = (this._dateExtent as [Date, Date]).map(d => d.getTime() ?? 0)\n        this._getBarsData(extent as [number, number])\n      }\n      this._maxCount = Math.max(...this._barsData.map(d => d.count))\n    }\n  }\n\n  /**  `setSelection`: Set the selected range on a `Timeline`. Takes a selection range as a parameter, which can be a range of dates or a range of numbers if `TimelineData` is numeric. */\n  public setSelection (selectionRange?: [Date, Date] | [number, number], renderOnly = false): void {\n    const prevSelection = this._currentSelection\n    if (selectionRange && this._dateExtent && selectionRange[0] >= this._dateExtent[0] && selectionRange[1] <= this._dateExtent[1] && selectionRange[0] < selectionRange[1]) {\n      this._currentSelection = selectionRange\n      this._currentSelectionInPixels = this._currentSelection.map(this._activeAxisScale) as [number, number]\n      this._animationControlDiv?.classList.remove(s.disabled)\n    } else {\n      this._currentSelection = undefined\n      this._currentSelectionInPixels = undefined\n      this._animationControlDiv?.classList.add(s.disabled)\n    }\n    const { _currentSelection } = this\n    if (!renderOnly && (prevSelection?.[0] !== _currentSelection?.[0] || prevSelection?.[1] !== _currentSelection?.[1])) {\n      this._config.events.onBrush?.(this._currentSelection, true)\n    }\n    if (this._brushInstance && !this._firstRender) {\n      this._brushGroup.call(this._brushInstance.move, this._currentSelectionInPixels as BrushSelection)\n    }\n  }\n\n  /**  `setSelectionInPixels`: Set the selected range on a `Timeline` in pixels. Takes an array containing two numeric values representing selection range in pixels. */\n  public setSelectionInPixels (coordinates?: [number, number]): void {\n    if (coordinates && coordinates[0] > 0 && coordinates[1] < this._timelineWidth && coordinates[0] < coordinates[1]) {\n      this._currentSelection = coordinates.map(d => this._activeAxisScale.invert(d)) as [Date, Date] | [number, number]\n      this._currentSelectionInPixels = this._currentSelection?.map(this._activeAxisScale) as [number, number]\n      this._animationControlDiv?.classList.remove(s.disabled)\n    } else {\n      this._currentSelection = undefined\n      this._currentSelectionInPixels = undefined\n      this._animationControlDiv?.classList.add(s.disabled)\n    }\n    if (this._brushInstance && !this._firstRender) {\n      this._brushGroup.call(this._brushInstance.move, this._currentSelectionInPixels as [number, number])\n    }\n    this._config.events.onBrush?.(this._currentSelection)\n  }\n\n  /**  `resize`: Resizes `Timeline` according to the parent node attributes. */\n  public resize (): void {\n    const { height, width } = getInnerDimensions(this._containerNode)\n    const { offsetWidth: animationWidth } = this._animationControlDiv\n    this._width = width\n    this._height = height\n    this._timelineWidth = this._width - this._config.padding.left - this._config.padding.right - animationWidth\n    this._timelineHeight = this._height - this._config.padding.top - this._config.padding.bottom\n    if (this._timelineHeight > (this._config.padding.top + this._config.padding.bottom)) {\n      this._updateScales()\n      this._checkLastTickPosition()\n      if (this._currentSelection) this.setSelection(this._currentSelection, true)\n      this.render()\n    }\n  }\n\n  /**  `render`: Renders `Timeline`. */\n  public render (): void {\n    this._updateBrush()\n    this._updateBars()\n    this._updateAxis()\n    if (this._firstRender) this._firstRender = false\n  }\n\n  private _updateAxis (): void {\n    if (!this._timeData) return\n    this._axisGroup\n      .style('transform', `translate(${this._config.padding.left}px, ${this._config.padding.top + this._config.axisTickHeight + 1 + this._config.selectionPadding / 2}px)`)\n      .call(this._isNumericTimeline ? this._numAxis : this._timeAxis)\n      .call(g => g.select('.domain').remove())\n\n    this._axisGroup.selectAll('.tick')\n      .select('text')\n      .attr('class', s.axisTick)\n      .attr('y', 0)\n      .attr('dy', -this._config.axisTickHeight)\n      .attr('dx', '5px')\n\n    this._axisGroup.selectAll('line')\n      .attr('class', s.axisLine)\n      .attr('y2', -this._config.axisTickHeight)\n  }\n\n  private _updateBrush (): void {\n    if (!this._config.allowSelection) return\n    this._brushGroup.style('transform', `translate(${this._config.padding.left}px, ${this._config.padding.top}px)`)\n    this._brushInstance = brushX().extent([[0, 0], [this._timelineWidth, this._timelineHeight]])\n    this._brushInstance.on('end', ({ selection, sourceEvent }: {selection: number[] | Date[]; sourceEvent: MouseEvent}) => {\n      if (!sourceEvent) return\n      if (selection) {\n        this._currentSelection = selection.map(d => this._activeAxisScale.invert(d)) as [Date, Date] | [number, number]\n        this._currentSelectionInPixels = this._currentSelection?.map(this._activeAxisScale) as [number, number]\n        this._animationControlDiv?.classList.remove(s.disabled)\n        this._config.events.onBrush?.(this._currentSelection)\n      } else {\n        this._currentSelection = undefined\n        this._currentSelectionInPixels = undefined\n        this._config.events.onBrush?.(undefined)\n        this._animationControlDiv?.classList.add(s.disabled)\n      }\n    })\n    this._brushGroup.call(this._brushInstance)\n    if (this._currentSelection) {\n      this._currentSelectionInPixels = this._currentSelection.map(this._activeAxisScale) as [number, number]\n      this._brushGroup.call(this._brushInstance.move, this._currentSelectionInPixels)\n    } else {\n      this._brushInstance?.clear(this._brushGroup)\n    }\n    this._brushGroup.select('rect.selection')\n      .classed(s.selection, true)\n      .attr('rx', this._config.selectionRadius)\n      .attr('ry', this._config.selectionRadius)\n  }\n\n  private _updateBars (): void {\n    this._barsGroup.style('transform', `translate(${this._config.padding.left}px, ${this._config.padding.top - this._config.selectionPadding / 2}px)`)\n    const bars = this._barsGroup.selectAll(`.${s.bar}`)\n      .data(this._barsData)\n      .join('rect')\n      .attr('class', s.bar)\n      .attr('x', d => this._activeAxisScale(+d.rangeStart) + this._barPadding / 2)\n      .attr('width', this.getBarWidth())\n      .attr('rx', this._config.barRadius)\n      .attr('ry', this._config.barRadius)\n      .attr('y', -this._timelineHeight)\n\n    if (this._config.events.onBarHover) bars.on('mouseover', this._config.events.onBarHover)\n\n    bars.transition().duration(300)\n      .attr('height', (d: BarData) => this._yScale(d.count))\n      .style('opacity', (d: BarData) => this._yScale(d.count) === this._config.minBarHeight ? 0.25 : 1)\n  }\n\n  private _updateScales (): void {\n    if (!this._dateExtent || !this._barsData.length) return\n    const lastBarData = this._barsData[this._barsData.length - 1] as BarData\n    if (this._config.tickStep) {\n      const ticks = range(+this._dateExtent[0], +this._dateExtent[1], this._config.tickStep)\n      if (this._isNumericTimeline) {\n        this._numAxis.tickValues(ticks)\n      } else {\n        this._timeAxis.tickValues(ticks.map(d => new Date(d)))\n      }\n    }\n    this._yScale\n      .range([this._config.minBarHeight, this._timelineHeight - this._config.barTopMargin - this._config.selectionPadding])\n      .domain([0, this._maxCount])\n      .clamp(true)\n    if (this._isNumericTimeline) {\n      this._numScale\n        .domain([this._dateExtent[0], lastBarData.rangeEnd])\n        .range([0, this._timelineWidth])\n        .clamp(true)\n      this._activeAxisScale = this._numScale\n    } else {\n      this._timeScale\n        .domain([this._dateExtent[0], lastBarData.rangeEnd])\n        .range([0, this._timelineWidth])\n        .clamp(true)\n      this._activeAxisScale = this._timeScale\n    }\n    const firstBar = this._barsData[0] as BarData\n    const diff = this._activeAxisScale(firstBar.rangeEnd) - this._activeAxisScale(firstBar.rangeStart)\n    this._barWidth = diff\n  }\n\n  private _disableBrush (): void {\n    this._brushInstance?.clear(this._brushGroup)\n    this._currentSelectionInPixels = undefined\n    this._currentSelection = undefined\n    this.pauseAnimation()\n    this._brushGroup.selectAll('*').remove()\n    if (this._config.showAnimationControls) this._animationControlDiv?.classList.add(s.disabled)\n  }\n\n  private async _initAnimationControls (): Promise<void> {\n    this._containerNode.insertBefore(this._animationControlDiv, this._svg)\n    const getIcons = async (): Promise<void> => {\n      if (!this._animationControlDiv.firstChild) {\n        const playButton = this._svgParser.parseFromString(play, 'image/svg+xml').firstChild as SVGElement\n        const pauseButton = this._svgParser.parseFromString(pause, 'image/svg+xml').firstChild as SVGElement\n        this._pauseButtonSvg = this._animationControlDiv?.appendChild(pauseButton)\n        this._playButtonSvg = this._animationControlDiv?.appendChild(playButton)\n      }\n    }\n    await getIcons().then(() => {\n      if (!this._isAnimationRunning) {\n        this._playButtonSvg?.classList.add(s.playAnimation)\n        this._pauseButtonSvg?.classList.add(s.pauseAnimation, s.hidden)\n      } else {\n        this._playButtonSvg?.classList.add(s.playAnimation, s.hidden)\n        this._pauseButtonSvg?.classList.add(s.pauseAnimation)\n      }\n      if (!this._currentSelection) this._animationControlDiv?.classList.add(s.disabled)\n      this._animationControlDiv.addEventListener('click', this._toggleAnimation)\n    })\n  }\n\n  private _toggleAnimation = (e: Event): void => {\n    e.preventDefault()\n    if (!this._isAnimationRunning) this.playAnimation()\n    else this.pauseAnimation()\n  }\n\n  private _disableAnimation = (): void => {\n    this.pauseAnimation()\n    this._animationControlDiv?.removeEventListener('click', this._toggleAnimation)\n    this._animationControlDiv?.remove()\n  }\n\n  /**  `playAnimation`: If some interval is selected on `Timeline`, starts animation for it. The selected interval is moved forward by each timeline bar according to the speed passed in the `animationSpeed` of the `Timeline` `config`. */\n  public playAnimation = (): void => {\n    clearInterval(this._animationInterval)\n    if (this._currentSelectionInPixels) {\n      this._animationInterval = setInterval(this._animateSelection, this._config.animationSpeed)\n      this._isAnimationRunning = true\n      this._config.events.onAnimationPlay?.(this._isAnimationRunning, this._currentSelection)\n    }\n    this._pauseButtonSvg?.classList.remove(s.hidden)\n    this._playButtonSvg?.classList.add(s.hidden)\n  }\n\n  /**  `pauseAnimation`: Pauses animation of selected timeline interval. */\n  public pauseAnimation = (): void => {\n    clearInterval(this._animationInterval)\n    this._isAnimationRunning = false\n    this._config.events.onAnimationPause?.(this._isAnimationRunning, this._currentSelection)\n    this._pauseButtonSvg?.classList.add(s.hidden)\n    this._playButtonSvg?.classList.remove(s.hidden)\n  }\n\n  /**  `stopAnimation`: Same as `pauseAnimation()`, but resets selection and returns `undefined` value for the `onBrush` callback. */\n  public stopAnimation = (): void => {\n    this.pauseAnimation()\n    this.setSelection(undefined)\n    this._config.events.onBrush?.(undefined)\n  }\n\n  private _animateSelection = (): void => {\n    const currentTimelineSelection = this._currentSelectionInPixels\n    if (!currentTimelineSelection) return\n    if (currentTimelineSelection[0] !== undefined && currentTimelineSelection[1] !== undefined) {\n      this.setSelectionInPixels([currentTimelineSelection[0] + this._barWidth, currentTimelineSelection[1] + this._barWidth])\n      if (currentTimelineSelection[1] === this._currentSelectionInPixels?.[1] || this._currentSelectionInPixels?.[1] === undefined) {\n        this.stopAnimation()\n      }\n    }\n  }\n\n  private _checkLastTickPosition = (): void => {\n    const lastTickSelection = this._axisGroup.selectAll('.tick:last-of-type').nodes()\n    if (lastTickSelection?.length) {\n      const lastTick = (lastTickSelection[0] as HTMLElement)\n      const lastTickRight = lastTick?.getBoundingClientRect().right\n      const svgRight = this._svg?.getBoundingClientRect().right\n      lastTick.style.display = lastTickRight >= svgRight ? 'none' : 'inherit'\n      // todo: runs twice with unusual small lastTickRight\n    }\n  }\n\n  public destroy = (): void => {\n    this._containerNode.innerHTML = ''\n    clearInterval(this._animationInterval)\n  }\n}\n\nexport { TimelineConfig }\nexport type { TimelineData, TimelineConfigInterface, TimelineEvents }\n"], "names": ["Timeline", "constructor", "containerNode", "config", "this", "_isAnimationRunning", "_svg<PERSON>arser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_height", "_width", "_timelineWidth", "_timelineHeight", "_config", "TimelineConfig", "_barWidth", "_maxCount", "_barsData", "_timeData", "undefined", "_bandIntervals", "_isNumericTimeline", "_firstRender", "_yScale", "scaleSymlog", "_timeScale", "scaleTime", "_numScale", "scaleLinear", "_activeAxisScale", "_timeAxis", "axisBottom", "_numAxis", "_toggleAnimation", "e", "preventDefault", "pauseAnimation", "playAnimation", "_disableAnimation", "_a", "_animationControlDiv", "removeEventListener", "_b", "remove", "clearInterval", "_animationInterval", "_currentSelectionInPixels", "setInterval", "_animateSelection", "animationSpeed", "events", "onAnimationPlay", "call", "_currentSelection", "_c", "_pauseButtonSvg", "classList", "s", "hidden", "_d", "_playButtonSvg", "add", "onAnimationPause", "stopAnimation", "setSelection", "onBrush", "currentTimelineSelection", "setSelectionInPixels", "_checkLastTickPosition", "lastTickSelection", "_axisGroup", "selectAll", "nodes", "length", "lastTick", "lastTickRight", "getBoundingClientRect", "right", "svgRight", "_svg", "style", "display", "destroy", "_containerNode", "innerHTML", "init", "document", "createElementNS", "timelineSvg", "createElement", "animationControl", "timeline", "append<PERSON><PERSON><PERSON>", "_noDataDiv", "select", "attr", "noData", "append", "text", "showAnimationControls", "elementExist", "_initAnimationControls", "_barsGroup", "bars", "axis", "_brushGroup", "brush", "tickFormat", "formatter", "_resizeObserver", "ResizeObserver", "entries", "window", "requestAnimationFrame", "Array", "isArray", "resize", "observe", "_barPadding", "barPadding", "getCurrentSelection", "getCurrentSelectionInPixels", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getConfig", "getIsAnimationRunning", "setConfig", "prevConfig", "JSON", "parse", "stringify", "isConnected", "allowSelection", "_disableBrush", "dataStep", "_e", "barCount", "_f", "_updateTimelineData", "setTimeData", "timeData", "filter", "d", "isNaN", "_updateScales", "_dateExtent", "extent", "_getBarsData", "rangeForIntervals", "date", "countedDates", "rollup", "v", "step", "range", "lastInterval", "map", "Date", "push", "datePairs", "pairs", "rangeStart", "rangeEnd", "count", "getCountsInRange", "getTime", "Math", "max", "<PERSON><PERSON><PERSON><PERSON>", "renderOnly", "prevSelection", "disabled", "_brushInstance", "move", "coordinates", "invert", "height", "width", "getInnerDimensions", "offsetWidth", "animationWidth", "padding", "left", "top", "bottom", "render", "_updateBrush", "_updateBars", "_updateAxis", "axisTickHeight", "selectionPadding", "g", "axisTick", "axisLine", "brushX", "on", "selection", "sourceEvent", "_g", "clear", "classed", "selectionRadius", "bar", "data", "join", "barRadius", "onBarHover", "transition", "duration", "minBarHeight", "lastBarData", "tickStep", "ticks", "tickValues", "bar<PERSON>op<PERSON>argin", "domain", "clamp", "firstBar", "diff", "insertBefore", "async", "<PERSON><PERSON><PERSON><PERSON>", "playButton", "parseFromString", "play", "pauseButton", "pause", "getIcons", "then", "addEventListener"], "mappings": "+jBAeaA,EA6CX,WAAAC,CAAoBC,EAA4BC,SAoB9C,GA/DMC,KAAmBC,qBAAG,EACtBD,KAAAE,WAAa,IAAIC,UAajBH,KAAOI,QAAG,EACVJ,KAAMK,OAAG,EACTL,KAAcM,eAAG,EACjBN,KAAeO,gBAAG,EAElBP,KAAAQ,QAAU,IAAIC,EAEdT,KAASU,UAAG,EACZV,KAASW,UAAG,EAEZX,KAASY,UAAc,GACvBZ,KAASa,eAAiBC,EAE1Bd,KAAce,eAAsB,GAGpCf,KAAkBgB,oBAAG,EACrBhB,KAAYiB,cAAG,EAEfjB,KAAOkB,QAAGC,IACVnB,KAAUoB,WAAGC,IACbrB,KAASsB,UAAGC,IACZvB,KAAAwB,iBAA0FxB,KAAKoB,WAE/FpB,KAAAyB,UAAYC,EAAiB1B,KAAKoB,YAClCpB,KAAA2B,SAAWD,EAAmB1B,KAAKsB,WA0WnCtB,KAAA4B,iBAAoBC,IAC1BA,EAAEC,iBACG9B,KAAKC,oBACLD,KAAK+B,iBADqB/B,KAAKgC,eACV,EAGpBhC,KAAiBiC,kBAAG,aAC1BjC,KAAK+B,iBACoB,QAAzBG,EAAAlC,KAAKmC,4BAAoB,IAAAD,GAAAA,EAAEE,oBAAoB,QAASpC,KAAK4B,kBAClC,QAA3BS,EAAArC,KAAKmC,4BAAsB,IAAAE,GAAAA,EAAAC,QAAQ,EAI9BtC,KAAagC,cAAG,iBACrBO,cAAcvC,KAAKwC,oBACfxC,KAAKyC,4BACPzC,KAAKwC,mBAAqBE,YAAY1C,KAAK2C,kBAAmB3C,KAAKQ,QAAQoC,gBAC3E5C,KAAKC,qBAAsB,EACW,QAAtCoC,KAAArC,KAAKQ,QAAQqC,QAAOC,uBAAkB,IAAAT,GAAAA,EAAAU,KAAAb,EAAAlC,KAAKC,oBAAqBD,KAAKgD,oBAEnD,QAApBC,EAAAjD,KAAKkD,uBAAe,IAAAD,GAAAA,EAAEE,UAAUb,OAAOc,EAAEC,QACtB,QAAnBC,EAAAtD,KAAKuD,sBAAc,IAAAD,GAAAA,EAAEH,UAAUK,IAAIJ,EAAEC,OAAO,EAIvCrD,KAAc+B,eAAG,iBACtBQ,cAAcvC,KAAKwC,oBACnBxC,KAAKC,qBAAsB,EACY,QAAvCoC,KAAArC,KAAKQ,QAAQqC,QAAOY,wBAAmB,IAAApB,GAAAA,EAAAU,KAAAb,EAAAlC,KAAKC,oBAAqBD,KAAKgD,mBAClD,QAApBC,EAAAjD,KAAKkD,uBAAe,IAAAD,GAAAA,EAAEE,UAAUK,IAAIJ,EAAEC,QACnB,QAAnBC,EAAAtD,KAAKuD,sBAAc,IAAAD,GAAAA,EAAEH,UAAUb,OAAOc,EAAEC,OAAO,EAI1CrD,KAAa0D,cAAG,aACrB1D,KAAK+B,iBACL/B,KAAK2D,kBAAa7C,GACS,QAA3BuB,GAAAH,EAAAlC,KAAKQ,QAAQqC,QAAOe,eAAO,IAAAvB,GAAAA,EAAAU,KAAAb,OAAGpB,EAAU,EAGlCd,KAAiB2C,kBAAG,aAC1B,MAAMkB,EAA2B7D,KAAKyC,0BACjCoB,QAC+B/C,IAAhC+C,EAAyB,SAAoD/C,IAAhC+C,EAAyB,KACxE7D,KAAK8D,qBAAqB,CAACD,EAAyB,GAAK7D,KAAKU,UAAWmD,EAAyB,GAAK7D,KAAKU,YACxGmD,EAAyB,MAAqC,UAA9B7D,KAAKyC,iCAAyB,IAAAP,OAAA,EAAAA,EAAG,UAA8CpB,aAAxCuB,EAAArC,KAAKyC,gDAA4B,KAC1GzC,KAAK0D,gBAER,EAGK1D,KAAsB+D,uBAAG,WAC/B,MAAMC,EAAoBhE,KAAKiE,WAAWC,UAAU,sBAAsBC,QAC1E,GAAIH,eAAAA,EAAmBI,OAAQ,CAC7B,MAAMC,EAAYL,EAAkB,GAC9BM,EAAgBD,aAAA,EAAAA,EAAUE,wBAAwBC,MAClDC,EAAsB,QAAXvC,EAAAlC,KAAK0E,YAAM,IAAAxC,OAAA,EAAAA,EAAAqC,wBAAwBC,MACpDH,EAASM,MAAMC,QAAUN,GAAiBG,EAAW,OAAS,SAE/D,GAGIzE,KAAO6E,QAAG,KACf7E,KAAK8E,eAAeC,UAAY,GAChCxC,cAAcvC,KAAKwC,mBAAmB,EAralCzC,GAAQC,KAAKQ,QAAQwE,KAAKjF,GAC9BC,KAAK8E,eAAiBhF,EAEtBE,KAAK0E,KAAOO,SAASC,gBAAgB,6BAA8B,OACnElF,KAAK0E,KAAKvB,UAAUK,IAAIJ,EAAE+B,aAC1BnF,KAAKmC,qBAAuB8C,SAASG,cAAc,OACnDpF,KAAKmC,qBAAqBgB,UAAUK,IAAIJ,EAAEiC,kBAE1CrF,KAAK8E,eAAe3B,UAAUK,IAAIJ,EAAEkC,UACpCtF,KAAK8E,eAAeS,YAAYvF,KAAK0E,MAErC1E,KAAKwF,WAAaP,SAASG,cAAc,OACzCK,EAAOzF,KAAKwF,YACTb,MAAM,UAAW,QACjBe,KAAK,QAAStC,EAAEuC,QAChBC,OAAO,OACPC,KAAK,oBACR7F,KAAK8E,eAAeS,YAAYvF,KAAKwF,YAErB,UAAZxF,KAAKQ,eAAO,IAAA0B,OAAA,EAAAA,EAAE4D,sBAAuB,CACvC,MAAMC,EAAerD,aAAY,KACH,OAAxB1C,KAAK8E,iBACP9E,KAAKgG,yBACLzD,cAAcwD,GACf,GACA,IACJ,CAED/F,KAAKiG,WAAaR,EAAOzF,KAAK0E,MAAMkB,OAAO,KAAKF,KAAK,QAAStC,EAAE8C,MAChElG,KAAKiE,WAAawB,EAAOzF,KAAK0E,MAAMkB,OAAO,KAAKF,KAAK,QAAStC,EAAE+C,MAChEnG,KAAKoG,YAAcX,EAAOzF,KAAK0E,MAAMkB,OAAO,KAAKF,KAAK,QAAStC,EAAEiD,OAEjErG,KAAKyB,UAAU6E,WAAWtG,KAAKQ,QAAQ+F,WACvCvG,KAAK2B,SAAS2E,WAAWtG,KAAKQ,QAAQ+F,WAEtCvG,KAAKwG,gBAAkB,IAAIC,GAAgBC,IAEzCC,OAAOC,uBAAsB,KACtBC,MAAMC,QAAQJ,IAAaA,EAAQtC,QAGxCpE,KAAK+G,QAAQ,GACb,IAEJ/G,KAAKwG,gBAAkB,IAAIC,GAAe,KAAQzG,KAAK+G,QAAQ,IAC/D/G,KAAKwG,gBAAgBQ,QAAQhH,KAAK8E,eACnC,CAED,eAAYmC,GACV,OAAOjH,KAAKU,UAAYV,KAAKQ,QAAQ0G,UACtC,CAGM,mBAAAC,GACL,OAAOnH,KAAKgD,iBACb,CAGM,2BAAAoE,GACL,OAAOpH,KAAKyC,yBACb,CAGM,WAAA4E,GACL,OAAOrH,KAAKU,UAAYV,KAAKiH,WAC9B,CAGM,SAAAK,GACL,OAAOtH,KAAKQ,OACb,CAGM,qBAAA+G,GACL,OAAOvH,KAAKC,mBACb,CAGM,SAAAuH,CAAWzH,mBAChB,MAAM0H,EAAaC,KAAKC,MAAMD,KAAKE,UAAU5H,KAAKQ,UAC7CT,EAGHC,KAAKQ,QAAQwE,KAAKjF,GAFlBC,KAAKQ,QAAU,IAAIC,GAIL,UAAZT,KAAKQ,eAAO,IAAA0B,OAAA,EAAAA,EAAE4D,wBACc,QAAzBzD,EAAArC,KAAKmC,4BAAoB,IAAAE,OAAA,EAAAA,EAAEwF,cAAa7H,KAAKgG,yBACzChG,KAAKmC,sBACdnC,KAAKiC,oBAEFjC,KAAKQ,QAAQsH,gBAChB9H,KAAK+H,gBAEH/H,KAAKQ,QAAQ+F,YACfvG,KAAKyB,UAAU6E,WAAWtG,KAAKQ,QAAQ+F,WACvCvG,KAAK2B,SAAS2E,WAAWtG,KAAKQ,QAAQ+F,aAGxB,QAAZtD,EAAAjD,KAAKQ,eAAO,IAAAyC,OAAA,EAAAA,EAAE+E,qBAAa1E,EAAAmE,EAAW1H,6BAAQiI,oBAAYC,EAAAjI,KAAKQ,8BAAS0H,aAAgC,QAAnBC,EAAAV,EAAW1H,cAAQ,IAAAoI,OAAA,EAAAA,EAAAD,WAC1GlI,KAAKoI,sBAEPpI,KAAK+G,QACN,CAGM,WAAAsB,CAAaC,aAClBtI,KAAKa,UAAYyH,aAAQ,EAARA,EAAUC,QAAOC,IAAMC,OAAOD,SAAY1H,IAAN0H,IACrDxI,KAAKgD,uBAAoBlC,EACK,QAA9BuB,GAAAH,EAAAlC,KAAKQ,QAAQqC,QAAOe,eAAU,IAAAvB,GAAAA,EAAAU,KAAAb,EAAAlC,KAAKgD,mBACnChD,KAAK0I,gBACLjD,EAAOzF,KAAKwF,YAAYb,MAAM,UAAW,SACvB,UAAd3E,KAAKa,iBAAS,IAAAoC,OAAA,EAAAA,EAAEmB,SAClBpE,KAAK2I,YAAcC,EAAO5I,KAAKa,WAC/Bb,KAAKoI,wBAELpI,KAAKY,UAAY,GACjBZ,KAAKiE,WAAWC,UAAU,KAAK5B,SAC/BtC,KAAKiG,WAAW/B,UAAU,KAAK5B,SAC/BtC,KAAKoG,YAAYlC,UAAU,KAAK5B,SAChCmD,EAAOzF,KAAKwF,YAAYb,MAAM,UAAW,SACzC3E,KAAKiB,cAAe,EAEvB,CAEO,YAAA4H,CAAcC,EAAqCC,WACzD,KAAID,EAAkB,IAAMA,EAAkB,MAC1B,QAAhB5G,EAAAlC,KAAKa,iBAAW,IAAAqB,OAAA,EAAAA,EAAAkC,SAAUpE,KAAK2I,YAAa,CAC9C,MAAMK,EAAeC,EAAOjJ,KAAKa,WAAWqI,GAAKA,EAAE9E,SAAQoE,GAAKA,IAC1DW,EAAgC,QAAzB9G,EAAArC,KAAKQ,QAAQwH,gBAAY,IAAA3F,EAAAA,GAACyG,EAAkB,GAAKA,EAAkB,KAAO9I,KAAKQ,QAAQ0H,SAAW,GAC/G,GAAa,IAATiB,EAAY,OAChBnJ,KAAKe,eAAiBqI,GAAON,EAAkB,IAAKA,EAAkB,GAAIK,GAC1E,MAAM9E,EAAWrE,KAAKe,eAAef,KAAKe,eAAeqD,OAAS,GAClE,IAAIiF,EAA8BrJ,KAAKQ,QAAQwH,UAAY3D,EAAW8E,EAAOL,EAAkB,GAC3FC,IACF/I,KAAKe,eAAiBf,KAAKe,eAAeuI,KAAId,GAAK,IAAIe,KAAKf,KAC5Da,EAAe,IAAIE,KAAKF,IAEtBhF,EAAWyE,EAAkB,IAAI9I,KAAKe,eAAeyI,KAAKH,GAC9D,MAAMI,EAAYC,EAAM1J,KAAKe,gBAC7Bf,KAAKY,UAAY6I,EAAUH,KAAId,IAAM,CACnCmB,WAAYnB,EAAE,GACdoB,SAAUpB,EAAE,GACZqB,MAAOC,EAAiBd,EAAcR,MAEzC,CACF,CAEO,mBAAAJ,SACN,IAAoB,QAAhBlG,EAAAlC,KAAKa,iBAAW,IAAAqB,OAAA,EAAAA,EAAAkC,SAAUpE,KAAK2I,YAAa,CAE9C,GADA3I,KAAKgB,qBAAuBhB,KAAKa,UAAU,aAAc0I,MACrDvJ,KAAKgB,mBACPhB,KAAK6I,aAAa7I,KAAK2I,iBAClB,CACL3I,KAAKa,UAAYb,KAAKa,UAAUyI,KAAId,GAAK,IAAIe,KAAKf,KAClD,MAAMI,EAAU5I,KAAK2I,YAA6BW,KAAId,UAAK,eAAAtG,EAAAsG,EAAEuB,yBAAa,CAAC,IAC3E/J,KAAK6I,aAAaD,EACnB,CACD5I,KAAKW,UAAYqJ,KAAKC,OAAOjK,KAAKY,UAAU0I,KAAId,GAAKA,EAAEqB,QACxD,CACF,CAGM,YAAAlG,CAAcuG,EAAkDC,GAAa,eAClF,MAAMC,EAAgBpK,KAAKgD,kBACvBkH,GAAkBlK,KAAK2I,aAAeuB,EAAe,IAAMlK,KAAK2I,YAAY,IAAMuB,EAAe,IAAMlK,KAAK2I,YAAY,IAAMuB,EAAe,GAAKA,EAAe,IACnKlK,KAAKgD,kBAAoBkH,EACzBlK,KAAKyC,0BAA4BzC,KAAKgD,kBAAkBsG,IAAItJ,KAAKwB,kBACxC,QAAzBU,EAAAlC,KAAKmC,4BAAoB,IAAAD,GAAAA,EAAEiB,UAAUb,OAAOc,EAAEiH,YAE9CrK,KAAKgD,uBAAoBlC,EACzBd,KAAKyC,+BAA4B3B,EACR,QAAzBuB,EAAArC,KAAKmC,4BAAoB,IAAAE,GAAAA,EAAEc,UAAUK,IAAIJ,EAAEiH,WAE7C,MAAMrH,kBAAEA,GAAsBhD,KACzBmK,IAAeC,aAAA,EAAAA,EAAgB,OAAOpH,aAAA,EAAAA,EAAoB,MAAMoH,eAAAA,EAAgB,OAAOpH,eAAAA,EAAoB,KACnF,QAA3BM,GAAAL,EAAAjD,KAAKQ,QAAQqC,QAAOe,eAAO,IAAAN,GAAAA,EAAAP,KAAAE,EAAGjD,KAAKgD,mBAAmB,GAEpDhD,KAAKsK,iBAAmBtK,KAAKiB,cAC/BjB,KAAKoG,YAAYrD,KAAK/C,KAAKsK,eAAeC,KAAMvK,KAAKyC,0BAExD,CAGM,oBAAAqB,CAAsB0G,iBACvBA,GAAeA,EAAY,GAAK,GAAKA,EAAY,GAAKxK,KAAKM,gBAAkBkK,EAAY,GAAKA,EAAY,IAC5GxK,KAAKgD,kBAAoBwH,EAAYlB,KAAId,GAAKxI,KAAKwB,iBAAiBiJ,OAAOjC,KAC3ExI,KAAKyC,0BAAoD,QAAxBP,EAAAlC,KAAKgD,yBAAmB,IAAAd,OAAA,EAAAA,EAAAoH,IAAItJ,KAAKwB,kBACzC,QAAzBa,EAAArC,KAAKmC,4BAAoB,IAAAE,GAAAA,EAAEc,UAAUb,OAAOc,EAAEiH,YAE9CrK,KAAKgD,uBAAoBlC,EACzBd,KAAKyC,+BAA4B3B,EACR,QAAzBmC,EAAAjD,KAAKmC,4BAAoB,IAAAc,GAAAA,EAAEE,UAAUK,IAAIJ,EAAEiH,WAEzCrK,KAAKsK,iBAAmBtK,KAAKiB,cAC/BjB,KAAKoG,YAAYrD,KAAK/C,KAAKsK,eAAeC,KAAMvK,KAAKyC,2BAEzB,QAA9BwF,GAAA3E,EAAAtD,KAAKQ,QAAQqC,QAAOe,eAAU,IAAAqE,GAAAA,EAAAlF,KAAAO,EAAAtD,KAAKgD,kBACpC,CAGM,MAAA+D,GACL,MAAM2D,OAAEA,EAAMC,MAAEA,GAAUC,EAAmB5K,KAAK8E,iBAC1C+F,YAAaC,GAAmB9K,KAAKmC,qBAC7CnC,KAAKK,OAASsK,EACd3K,KAAKI,QAAUsK,EACf1K,KAAKM,eAAiBN,KAAKK,OAASL,KAAKQ,QAAQuK,QAAQC,KAAOhL,KAAKQ,QAAQuK,QAAQvG,MAAQsG,EAC7F9K,KAAKO,gBAAkBP,KAAKI,QAAUJ,KAAKQ,QAAQuK,QAAQE,IAAMjL,KAAKQ,QAAQuK,QAAQG,OAClFlL,KAAKO,gBAAmBP,KAAKQ,QAAQuK,QAAQE,IAAMjL,KAAKQ,QAAQuK,QAAQG,SAC1ElL,KAAK0I,gBACL1I,KAAK+D,yBACD/D,KAAKgD,mBAAmBhD,KAAK2D,aAAa3D,KAAKgD,mBAAmB,GACtEhD,KAAKmL,SAER,CAGM,MAAAA,GACLnL,KAAKoL,eACLpL,KAAKqL,cACLrL,KAAKsL,cACDtL,KAAKiB,eAAcjB,KAAKiB,cAAe,EAC5C,CAEO,WAAAqK,GACDtL,KAAKa,YACVb,KAAKiE,WACFU,MAAM,YAAa,aAAa3E,KAAKQ,QAAQuK,QAAQC,WAAWhL,KAAKQ,QAAQuK,QAAQE,IAAMjL,KAAKQ,QAAQ+K,eAAiB,EAAIvL,KAAKQ,QAAQgL,iBAAmB,QAC7JzI,KAAK/C,KAAKgB,mBAAqBhB,KAAK2B,SAAW3B,KAAKyB,WACpDsB,MAAK0I,GAAKA,EAAEhG,OAAO,WAAWnD,WAEjCtC,KAAKiE,WAAWC,UAAU,SACvBuB,OAAO,QACPC,KAAK,QAAStC,EAAEsI,UAChBhG,KAAK,IAAK,GACVA,KAAK,MAAO1F,KAAKQ,QAAQ+K,gBACzB7F,KAAK,KAAM,OAEd1F,KAAKiE,WAAWC,UAAU,QACvBwB,KAAK,QAAStC,EAAEuI,UAChBjG,KAAK,MAAO1F,KAAKQ,QAAQ+K,gBAC7B,CAEO,YAAAH,SACDpL,KAAKQ,QAAQsH,iBAClB9H,KAAKoG,YAAYzB,MAAM,YAAa,aAAa3E,KAAKQ,QAAQuK,QAAQC,WAAWhL,KAAKQ,QAAQuK,QAAQE,UACtGjL,KAAKsK,eAAiBsB,IAAShD,OAAO,CAAC,CAAC,EAAG,GAAI,CAAC5I,KAAKM,eAAgBN,KAAKO,mBAC1EP,KAAKsK,eAAeuB,GAAG,OAAO,EAAGC,YAAWC,oCACrCA,IACDD,GACF9L,KAAKgD,kBAAoB8I,EAAUxC,KAAId,GAAKxI,KAAKwB,iBAAiBiJ,OAAOjC,KACzExI,KAAKyC,0BAAoD,QAAxBP,EAAAlC,KAAKgD,yBAAmB,IAAAd,OAAA,EAAAA,EAAAoH,IAAItJ,KAAKwB,kBACzC,QAAzBa,EAAArC,KAAKmC,4BAAoB,IAAAE,GAAAA,EAAEc,UAAUb,OAAOc,EAAEiH,UAChB,QAA9B/G,GAAAL,EAAAjD,KAAKQ,QAAQqC,QAAOe,eAAU,IAAAN,GAAAA,EAAAP,KAAAE,EAAAjD,KAAKgD,qBAEnChD,KAAKgD,uBAAoBlC,EACzBd,KAAKyC,+BAA4B3B,EACN,QAA3BqH,GAAAF,EAAAjI,KAAKQ,QAAQqC,QAAOe,eAAO,IAAAuE,GAAAA,EAAApF,KAAAkF,OAAGnH,GACL,QAAzBkL,EAAAhM,KAAKmC,4BAAoB,IAAA6J,GAAAA,EAAE7I,UAAUK,IAAIJ,EAAEiH,WAC5C,IAEHrK,KAAKoG,YAAYrD,KAAK/C,KAAKsK,gBACvBtK,KAAKgD,mBACPhD,KAAKyC,0BAA4BzC,KAAKgD,kBAAkBsG,IAAItJ,KAAKwB,kBACjExB,KAAKoG,YAAYrD,KAAK/C,KAAKsK,eAAeC,KAAMvK,KAAKyC,4BAEhC,QAArBP,EAAAlC,KAAKsK,sBAAgB,IAAApI,GAAAA,EAAA+J,MAAMjM,KAAKoG,aAElCpG,KAAKoG,YAAYX,OAAO,kBACrByG,QAAQ9I,EAAE0I,WAAW,GACrBpG,KAAK,KAAM1F,KAAKQ,QAAQ2L,iBACxBzG,KAAK,KAAM1F,KAAKQ,QAAQ2L,iBAC5B,CAEO,WAAAd,GACNrL,KAAKiG,WAAWtB,MAAM,YAAa,aAAa3E,KAAKQ,QAAQuK,QAAQC,WAAWhL,KAAKQ,QAAQuK,QAAQE,IAAMjL,KAAKQ,QAAQgL,iBAAmB,QAC3I,MAAMtF,EAAOlG,KAAKiG,WAAW/B,UAAU,IAAId,EAAEgJ,OAC1CC,KAAKrM,KAAKY,WACV0L,KAAK,QACL5G,KAAK,QAAStC,EAAEgJ,KAChB1G,KAAK,KAAK8C,GAAKxI,KAAKwB,kBAAkBgH,EAAEmB,YAAc3J,KAAKiH,YAAc,IACzEvB,KAAK,QAAS1F,KAAKqH,eACnB3B,KAAK,KAAM1F,KAAKQ,QAAQ+L,WACxB7G,KAAK,KAAM1F,KAAKQ,QAAQ+L,WACxB7G,KAAK,KAAM1F,KAAKO,iBAEfP,KAAKQ,QAAQqC,OAAO2J,YAAYtG,EAAK2F,GAAG,YAAa7L,KAAKQ,QAAQqC,OAAO2J,YAE7EtG,EAAKuG,aAAaC,SAAS,KACxBhH,KAAK,UAAW8C,GAAexI,KAAKkB,QAAQsH,EAAEqB,SAC9ClF,MAAM,WAAY6D,GAAexI,KAAKkB,QAAQsH,EAAEqB,SAAW7J,KAAKQ,QAAQmM,aAAe,IAAO,GAClG,CAEO,aAAAjE,GACN,IAAK1I,KAAK2I,cAAgB3I,KAAKY,UAAUwD,OAAQ,OACjD,MAAMwI,EAAc5M,KAAKY,UAAUZ,KAAKY,UAAUwD,OAAS,GAC3D,GAAIpE,KAAKQ,QAAQqM,SAAU,CACzB,MAAMC,EAAQ1D,GAAOpJ,KAAK2I,YAAY,IAAK3I,KAAK2I,YAAY,GAAI3I,KAAKQ,QAAQqM,UACzE7M,KAAKgB,mBACPhB,KAAK2B,SAASoL,WAAWD,GAEzB9M,KAAKyB,UAAUsL,WAAWD,EAAMxD,KAAId,GAAK,IAAIe,KAAKf,KAErD,CACDxI,KAAKkB,QACFkI,MAAM,CAACpJ,KAAKQ,QAAQmM,aAAc3M,KAAKO,gBAAkBP,KAAKQ,QAAQwM,aAAehN,KAAKQ,QAAQgL,mBAClGyB,OAAO,CAAC,EAAGjN,KAAKW,YAChBuM,OAAM,GACLlN,KAAKgB,oBACPhB,KAAKsB,UACF2L,OAAO,CAACjN,KAAK2I,YAAY,GAAIiE,EAAYhD,WACzCR,MAAM,CAAC,EAAGpJ,KAAKM,iBACf4M,OAAM,GACTlN,KAAKwB,iBAAmBxB,KAAKsB,YAE7BtB,KAAKoB,WACF6L,OAAO,CAACjN,KAAK2I,YAAY,GAAIiE,EAAYhD,WACzCR,MAAM,CAAC,EAAGpJ,KAAKM,iBACf4M,OAAM,GACTlN,KAAKwB,iBAAmBxB,KAAKoB,YAE/B,MAAM+L,EAAWnN,KAAKY,UAAU,GAC1BwM,EAAOpN,KAAKwB,iBAAiB2L,EAASvD,UAAY5J,KAAKwB,iBAAiB2L,EAASxD,YACvF3J,KAAKU,UAAY0M,CAClB,CAEO,aAAArF,WACe,QAArB7F,EAAAlC,KAAKsK,sBAAgB,IAAApI,GAAAA,EAAA+J,MAAMjM,KAAKoG,aAChCpG,KAAKyC,+BAA4B3B,EACjCd,KAAKgD,uBAAoBlC,EACzBd,KAAK+B,iBACL/B,KAAKoG,YAAYlC,UAAU,KAAK5B,SAC5BtC,KAAKQ,QAAQsF,wBAAgD,QAAzBzD,EAAArC,KAAKmC,4BAAoB,IAAAE,GAAAA,EAAEc,UAAUK,IAAIJ,EAAEiH,UACpF,CAEO,4BAAMrE,GACZhG,KAAK8E,eAAeuI,aAAarN,KAAKmC,qBAAsBnC,KAAK0E,WAChD4I,mBACf,IAAKtN,KAAKmC,qBAAqBoL,WAAY,CACzC,MAAMC,EAAaxN,KAAKE,WAAWuN,gBAAgBC,EAAM,iBAAiBH,WACpEI,EAAc3N,KAAKE,WAAWuN,gBAAgBG,EAAO,iBAAiBL,WAC5EvN,KAAKkD,gBAA2C,QAAzBhB,EAAAlC,KAAKmC,4BAAoB,IAAAD,OAAA,EAAAA,EAAEqD,YAAYoI,GAC9D3N,KAAKuD,eAA0C,QAAzBlB,EAAArC,KAAKmC,4BAAoB,IAAAE,OAAA,EAAAA,EAAEkD,YAAYiI,EAC9D,GAEGK,GAAWC,MAAK,mBACf9N,KAAKC,qBAIa,QAArBgD,EAAAjD,KAAKuD,sBAAgB,IAAAN,GAAAA,EAAAE,UAAUK,IAAIJ,EAAEpB,cAAeoB,EAAEC,QAClC,QAApBC,EAAAtD,KAAKkD,uBAAe,IAAAI,GAAAA,EAAEH,UAAUK,IAAIJ,EAAErB,kBAJnB,QAAnBG,EAAAlC,KAAKuD,sBAAc,IAAArB,GAAAA,EAAEiB,UAAUK,IAAIJ,EAAEpB,eACf,QAAtBK,EAAArC,KAAKkD,uBAAiB,IAAAb,GAAAA,EAAAc,UAAUK,IAAIJ,EAAErB,eAAgBqB,EAAEC,SAKrDrD,KAAKgD,mBAA4C,QAAzBiF,EAAAjI,KAAKmC,4BAAoB,IAAA8F,GAAAA,EAAE9E,UAAUK,IAAIJ,EAAEiH,UACxErK,KAAKmC,qBAAqB4L,iBAAiB,QAAS/N,KAAK4B,iBAAiB,GAE7E"}