## About
The cosmos.gl awesome list is a list of awesome things curated by the community.

## cosmos.gl Examples:
**MathWorks/Kuu** The MathWorks, Inc: symmetric positive definite matrix
- Source: https://sparse.tamu.edu/MathWorks/Kuu
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/Kuu.csv

**HB/blckhole** S CONNECTIVITY STRUCTURE OF A GEODESIC DOME ON A COARSE BASE
- Source: https://sparse.tamu.edu/HB/blckhole
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/blckhole.csv

**HB/can_229** SYMMETRIC PATTERN FROM CANNES,LUCIEN MARRO,JUNE 1981.
- Source: https://sparse.tamu.edu/HB/can_229
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/can_229.csv
 
**Gset/G34** 2D torus, 40-by-50, uniformly random +1/-1 entries
- Source: https://sparse.tamu.edu/Gset/G34
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/G34.csv

**Pothen/sphere3** sphere3, with coordinates. From NASA, collected by Alex Pothen
- Source: https://sparse.tamu.edu/Pothen/sphere3
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/sphere3.csv

**Bydder/mri1** MRI reconstruction (1), from Mark Bydder, UCSD
- Source: https://sparse.tamu.edu/Bydder/mri1
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/mri1.csv

**Mittelmann/pds-90** Patient distribution (evacuation) system
- Source: https://sparse.tamu.edu/Mittelmann/pds-90
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/pds-90.csv

**Chen/pkustk10** PKU SYMMETRIC STIFFNESS, 4 TOWER SILO
- Source: https://sparse.tamu.edu/Chen/pkustk10
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/pkustk10.csv

**GHS_indef/aug3d** Gould, Hu, & Scott: expanded system-3D PDE (CUTEr)
- Source: https://sparse.tamu.edu/GHS_indef/aug3d
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/aug3d.csv

**Nasa/barth5** BARTH5: Nasa matrix, but with a diagonal added to the original matrix
- Source: https://sparse.tamu.edu/Nasa/barth5
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/barth5.csv

**DIMACS10/delaunay_n10**
DIMACS10 set: delaunay/delaunay_n10
- Source: https://sparse.tamu.edu/DIMACS10/delaunay_n10
- Example:https://cosmograph.app/run/?data=https://cosmograph.app/data/delaunay_n10.csv

**HB/dwt_1005** SYMMETRIC CONNECTION TABLE FROM DTNSRDC, WASHINGTON
- Source: https://sparse.tamu.edu/HB/dwt_1005
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/dwt_1005.csv

**HB/gre_1107** UNSYMMETRIC MATRIX FROM GRENOBLE,FRANCOIS CACHARD,MARCH 1981.
- Source: https://sparse.tamu.edu/HB/gre_1107
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/gre_1107.csv

**Hollinger/jan99jac040sc** Jacobian from Bank of Canada ‘jan99’ model, oldstack 040, with scaling
- Source: https://sparse.tamu.edu/Hollinger/jan99jac040sc
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/jan99jac040sc.csv

**HB/man_5976** MANTEUFFEL’S FINITE ELEMENT PROBLEM (CONDENSED)
- Source: https://sparse.tamu.edu/HB/man_5976
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/man_5976.csv

**Boeing/msc01440** SYMMETRIC TEST MATRIX FROM MSC/NASTRAN CYLF8.OUT2
- Source: https://sparse.tamu.edu/Boeing/msc01440
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/msc01440.csv

**Nasa/nasa4704** STRUCTURE FROM NASA LANGLEY, 4704 DEGREES OF FREEDOM
- Source: https://sparse.tamu.edu/Nasa/nasa4704
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/nasa4704.csv

**Bai/rw5151** MARKOV CHAIN MODELING, RANDOM WALK (M = 100) G. W. STEWART
- Source: https://sparse.tamu.edu/Bai/rw5151
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/rw5151.csv

**Shyy/shyy41** Wei Shyy, Univ. Florida. CFD/Navier-Stokes,viscous flow, fully coupled
- Source: https://sparse.tamu.edu/Shyy/shyy41
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/shyy41.csv

**Shyy/shyy161** Wei Shyy, Univ. Florida. CFD/Navier-Stokes,viscous flow, fully coupled
- Source: https://sparse.tamu.edu/Shyy/shyy161
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/shyy161.csv

**HB/young3c** real matrix from aero research, David Young, corrected RUA version
- Source: https://sparse.tamu.edu/HB/young3c
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/young3c.csv

**TKK/cyl6** Cylindrical shell, non-linear analysis, 3 RHS vectors at step 6. R Kouhia
- Source: https://sparse.tamu.edu/TKK/cyl6
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/cyl6.csv

**GHS_psdef/cvxbqp1** Gould, Hu, & Scott: barrier Hessian from convex QP (CUTEr)
- Source: https://sparse.tamu.edu/GHS_psdef/cvxbqp1
- Example: https://cosmograph.app/run/?data=https://cosmograph.app/data/cvxbqp1.csv
