const t=t=>"function"==typeof t,e=t=>Array.isArray(t),n=t=>t instanceof Object,r=t=>"Function"!==t.constructor.name&&"Object"!==t.constructor.name,o=o=>n(o)&&!e(o)&&!t(o)&&!r(o),s=(t,e=new Map)=>{if("object"!=typeof t||null===t)return t;if(t instanceof Date)return new Date(t.getTime());if(t instanceof Array){const n=[];e.set(t,n);for(const r of t)n.push(e.has(r)?e.get(r):s(r,e));return t}if(r(t)){return t}if(t instanceof Object){const n={};e.set(t,n);const r=t;return Object.keys(t).reduce(((t,n)=>(t[n]=e.has(r[n])?e.get(r[n]):s(r[n],e),t)),n),n}return t},a=(t,e,n=new Map)=>{const c=r(t)?t:s(t);return t===e?t:n.has(e)?n.get(e):(n.set(e,c),Object.keys(e).forEach((i=>{o(t[i])&&o(e[i])?c[i]=a(t[i],e[i],n):r(e)?c[i]=e:c[i]=s(e[i])})),c)},c=(t,e,n)=>t>=+e&&+t<=+n,i=(t,e)=>{const[n,r]=e,o=Array.from(t.keys());let s=0;return o.forEach((e=>{var o;c(+e,+n,+r)&&(s+=null!==(o=t.get(e))&&void 0!==o?o:0)})),s},f=t=>{const e=getComputedStyle(t);let n=t.clientWidth,r=t.clientHeight;return r-=parseFloat(e.paddingTop)+parseFloat(e.paddingBottom),n-=parseFloat(e.paddingLeft)+parseFloat(e.paddingRight),{height:r,width:n}};class u{init(t){const e=this;return Object.keys(t).forEach((n=>{o(e[n])?e[n]=a(e[n],t[n]):e[n]=t[n]})),this}}export{u as Config,s as cloneDeep,i as getCountsInRange,f as getInnerDimensions,r as isAClassInstance,e as isArray,c as isBetween,t as isFunction,n as isObject,o as isPlainObject,a as merge};
//# sourceMappingURL=utils.js.map
