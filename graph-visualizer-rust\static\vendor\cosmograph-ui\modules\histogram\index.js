import{axisBottom as t}from"d3-axis";import{ResizeObserver as i}from"@juggle/resize-observer";import{brushX as s}from"d3-brush";import{scaleSymlog as e,scaleLinear as a}from"d3-scale";import{select as h}from"d3-selection";import{extent as n,rollup as r,pairs as o}from"d3-array";import{HistogramConfig as _}from"./config.js";export{HistogramConfig}from"./config.js";import{getCountsInRange as l}from"../../utils.js";import c from"./style.module.css.js";class d{constructor(s,n){var r;this._config=new _,this._firstRender=!0,this._barWidth=0,this._maxCount=0,this._barsData=[],this._highlightedBarsData=[],this._histogramData=void 0,this._highlightedData=void 0,this._bandIntervals=[],this._calculatedStep=0,this._yScale=e(),this._xScale=a(),this._axis=t(this._xScale),this._getClosestRange=(t,i)=>{var s;return null===(s=this._barsData)||void 0===s?void 0:s.reduce(((s,e)=>Math.abs(e[i?"rangeEnd":"rangeStart"]-t)<Math.abs(s[i?"rangeEnd":"rangeStart"]-t)?e:s))};const{offsetWidth:o,offsetHeight:l}=s;n&&this._config.init(n),this._containerNode=s,this._containerNode.classList.add(c.histogram),this._width=o,this._height=l,this._formatter=null!==(r=null==n?void 0:n.formatter)&&void 0!==r?r:void 0,this._histogramWidth=this._width-this._config.padding.left-this._config.padding.right,this._histogramHeight=this._height-this._config.padding.top-this._config.padding.bottom,this._svg=document.createElementNS("http://www.w3.org/2000/svg","svg"),this._svg.classList.add(c.histogramSvg),this._containerNode.appendChild(this._svg),this._noDataDiv=document.createElement("div"),h(this._noDataDiv).style("display","none").attr("class",c.noData).append("div").text("No histogram data"),this._containerNode.appendChild(this._noDataDiv),this._axisGroup=h(this._svg).append("g").attr("class",c.axis),this._barsGroup=h(this._svg).append("g").attr("class",c.bars),this._highlightedBarsGroup=h(this._svg).append("g").attr("class",c.bars),this._brushGroup=h(this._svg).append("g").attr("class",c.brush),this._axis.tickFormat((t=>this._formatter?this._formatter(t):t.toFixed(0))),this._resizeObserver=new i((()=>{this.resize()})),this._resizeObserver.observe(this._containerNode)}get _barPadding(){return this._barWidth*this._config.barPadding}get getCurrentSelection(){return this._currentSelection}get getCurrentSelectionInPixels(){var t;return null!==(t=this._currentSelectionInPixels)&&void 0!==t?t:[]}getBarWidth(){return this._barWidth-this._barPadding}getConfig(){return this._config}setConfig(t){var i,s,e,a;const h=JSON.parse(JSON.stringify(this._config));t?this._config.init(t):this._config=new _,this._config.allowSelection||this._disableBrush(),(null===(i=this._config)||void 0===i?void 0:i.dataStep)===(null===(s=h.config)||void 0===s?void 0:s.dataStep)&&(null===(e=this._config)||void 0===e?void 0:e.barCount)===(null===(a=h.config)||void 0===a?void 0:a.barCount)||(this._updateHistogramData(),this._updateHistogramHighlightedData()),this.resize()}setHistogramData(t,i){var s,e,a;i&&i[1]<=i[0]||(this._histogramData=null==t?void 0:t.filter((t=>!isNaN(t)&&void 0!==t)),this._highlightedBarsData=[],this._currentSelection=void 0,this._brushCurrentSelection(),null===(e=(s=this._config.events).onBrush)||void 0===e||e.call(s,this._currentSelection),h(this._noDataDiv).style("display","none"),(null===(a=this._histogramData)||void 0===a?void 0:a.length)?(this._extent=i||n(this._histogramData),this._firstRender=!0,this._updateHistogramData(),this._updateScales(),this.render()):h(this._noDataDiv).style("display","block"))}setHighlightedData(t){if(this._histogramData)if(t&&0!==t.length)this._highlightedData=null==t?void 0:t.filter((t=>!isNaN(t)&&void 0!==t)),this._updateHistogramHighlightedData(),this._updateBars(!0);else{const t=JSON.stringify(this._highlightedBarsData);this._highlightedData=[],this._highlightedBarsData=[],t!==JSON.stringify(this._highlightedBarsData)&&this._updateBars(!0)}}setSelection(t,i=!1){var s,e;const a=this._currentSelection;if(t&&this._extent&&t[0]>=this._extent[0]&&t[1]<=this._extent[1]&&t[0]<t[1]){const i=t.map(this._xScale);this._mapSelection(i)}else this._currentSelection=void 0;const{_currentSelection:h}=this;i||(null==a?void 0:a[0])===(null==h?void 0:h[0])&&(null==a?void 0:a[1])===(null==h?void 0:h[1])||null===(e=(s=this._config.events).onBrush)||void 0===e||e.call(s,this._currentSelection,!0),this._brushCurrentSelection()}resize(){const{offsetWidth:t,offsetHeight:i}=this._containerNode;this._width=t,this._height=i,this._histogramWidth=this._width-this._config.padding.left-this._config.padding.right,this._histogramHeight=this._height-this._config.padding.top-this._config.padding.bottom,this._histogramHeight>this._config.padding.top+this._config.padding.bottom&&(this._updateScales(),this._currentSelection&&this.setSelection(this._currentSelection,!0),this._firstRender||this.render())}render(){this._updateBrush(),this._updateBars(),this._updateBars(!0),this._updateAxis(),this._firstRender&&(this._firstRender=!1)}destroy(){this._containerNode.innerHTML=""}_updateAxis(){this._histogramData&&(this._axisGroup.style("transform",`translate(${this._config.padding.left}px, ${this._config.padding.top-this._config.selectionPadding/2+1}px)`).call(this._axis).call((t=>t.select(".domain").remove())),this._axisGroup.selectAll(".tick").select("text").attr("class",c.axisTick).attr("y",0).attr("dy",0).attr("dx",this._config.labelSideMargin),this._axisGroup.selectAll(".tick:last-of-type text").attr("dx",-this._config.labelSideMargin).style("text-anchor","end"),this._axisGroup.selectAll("line").attr("y2",this._histogramHeight).attr("y1",0).attr("opacity",.1))}_updateBrush(){this._config.allowSelection&&(this._brushGroup.style("transform",`translate(${this._config.padding.left}px, ${this._config.padding.top}px)`),this._brushInstance=s().extent([[0,0],[this._histogramWidth,this._histogramHeight]]),this._brushInstance.on("end",(({selection:t,sourceEvent:i})=>{var s,e,a,h;i&&(t?(this._mapSelection(t),null===(e=(s=this._config.events).onBrush)||void 0===e||e.call(s,this._currentSelection)):(this._currentSelection=void 0,null===(h=(a=this._config.events).onBrush)||void 0===h||h.call(a,void 0)),this._brushCurrentSelection())})),this._brushGroup.call(this._brushInstance),this._brushGroup.select("rect.selection").classed(c.selection,!0).attr("rx",this._config.selectionRadius).attr("ry",this._config.selectionRadius))}_updateBars(t){const i=t?this._highlightedBarsGroup:this._barsGroup;i.style("transform",`translate(${this._config.padding.left}px, ${this._config.padding.top-this._config.selectionPadding/2}px)`);const s=t?.5:1,e=i.selectAll(`.${t?c.highlightedBar:c.bar}`).data(t?this._highlightedBarsData:this._barsData).join("rect").attr("class",t?c.highlightedBar:c.bar).attr("x",(t=>this._xScale(t.rangeStart)+this._barPadding/2)).attr("width",this.getBarWidth()).attr("rx",this._config.barRadius).attr("ry",this._config.barRadius).attr("y",-this._histogramHeight);t||this._config.events.onBarHover&&e.on("mouseover",this._config.events.onBarHover),e.transition().duration(300).attr("height",(i=>t&&0===i.count?0:this._yScale(i.count))).style("opacity",(t=>this._yScale(t.count)===this._config.minBarHeight?.4:s))}_updateScales(){if(!this._extent||!this._barsData.length)return;const t=this._barsData[this._barsData.length-1],i=this._config.dataStep?[this._extent[0],t.rangeEnd]:this._extent;this._xScale.domain(i).range([0,this._histogramWidth]).clamp(!0),this._yScale.range([this._config.minBarHeight,this._histogramHeight-this._config.barTopMargin-this._config.selectionPadding]).domain([0,this._maxCount]).clamp(!0),this._axis.tickValues(i),this._barWidth=this._xScale(t.rangeEnd)-this._xScale(t.rangeStart)}_disableBrush(){this._currentSelection=void 0,this._brushCurrentSelection(),this._brushGroup.selectAll("*").remove()}_updateHistogramData(){var t,i;if((null===(t=this._histogramData)||void 0===t?void 0:t.length)&&this._extent){const t=r(this._histogramData,(t=>t.length),(t=>t)),s=null!==(i=this._config.dataStep)&&void 0!==i?i:Math.abs(this._extent[1]-this._extent[0])/(this._config.barCount-1);if(0===s)return;if(this._calculatedStep=s,this._bandIntervals=this._generateSequence(this._extent[0],this._extent[1],this._calculatedStep),this._config.dataStep){const t=this._bandIntervals[this._bandIntervals.length-1];t<this._extent[1]&&this._bandIntervals.push(+t+this._calculatedStep)}const e=o(this._bandIntervals);this._barsData=e.map((i=>({rangeStart:i[0],rangeEnd:i[1],count:l(t,i)}))),this._maxCount=Math.max(...this._barsData.map((t=>t.count)))}}_updateHistogramHighlightedData(){var t;if((null===(t=this._highlightedData)||void 0===t?void 0:t.length)&&this._extent){const t=r(this._highlightedData,(t=>t.length),(t=>t));this._bandIntervals=this._generateSequence(this._extent[0],this._extent[1],this._calculatedStep);const i=o(this._bandIntervals);this._highlightedBarsData=i.map(((i,s)=>{let e=l(t,i);const a=this._barsData[s];return a&&e>this._barsData[s].count&&(e=this._barsData[s].count),{rangeStart:a.rangeStart,rangeEnd:a.rangeEnd,count:e}}))}}_mapSelection(t){this._barsData.length&&(this._config.stickySelection?(this._currentSelection=[this._getClosestRange(this._xScale.invert(t[0])).rangeStart,this._getClosestRange(this._xScale.invert(t[1]),!0).rangeEnd],this._currentSelection[0]===this._currentSelection[1]&&(this._currentSelection=void 0)):this._currentSelection=t.map(this._xScale.invert))}_brushCurrentSelection(){var t;this._currentSelection?(this._currentSelectionInPixels=this._currentSelection.map(this._xScale),this._brushInstance&&!this._firstRender&&this._brushGroup.call(this._brushInstance.move,this._currentSelectionInPixels)):(this._currentSelectionInPixels=void 0,null===(t=this._brushInstance)||void 0===t||t.clear(this._brushGroup))}_generateSequence(t,i,s){const e=Math.round((i-t)/s)+1,a=new Array(e);for(let i=0;i<e;i++)a[i]=t+i*s;return a}}export{d as Histogram};
//# sourceMappingURL=index.js.map
