import{__extends as t,__assign as e,__values as a}from"../../../_virtual/_tslib.js";import{MDCFoundation as i}from"../base/foundation.js";import{cssClasses as r,strings as n,numbers as s}from"./constants.js";import{getNormalizedEventCoords as o}from"./util.js";
/**
 * @license
 * Copyright 2016 Google Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
var d=["touchstart","pointerdown","mousedown","keydown"],c=["touchend","pointerup","mouseup","contextmenu"],u=[],l=function(i){function l(t){var a=i.call(this,e(e({},l.defaultAdapter),t))||this;return a.activationAnimationHasEnded=!1,a.activationTimer=0,a.fgDeactivationRemovalTimer=0,a.fgScale="0",a.frame={width:0,height:0},a.initialSize=0,a.layoutFrame=0,a.maxRadius=0,a.unboundedCoords={left:0,top:0},a.activationState=a.defaultActivationState(),a.activationTimerCallback=function(){a.activationAnimationHasEnded=!0,a.runDeactivationUXLogicIfReady()},a.activateHandler=function(t){a.activateImpl(t)},a.deactivateHandler=function(){a.deactivateImpl()},a.focusHandler=function(){a.handleFocus()},a.blurHandler=function(){a.handleBlur()},a.resizeHandler=function(){a.layout()},a}return t(l,i),Object.defineProperty(l,"cssClasses",{get:function(){return r},enumerable:!1,configurable:!0}),Object.defineProperty(l,"strings",{get:function(){return n},enumerable:!1,configurable:!0}),Object.defineProperty(l,"numbers",{get:function(){return s},enumerable:!1,configurable:!0}),Object.defineProperty(l,"defaultAdapter",{get:function(){return{addClass:function(){},browserSupportsCssVars:function(){return!0},computeBoundingRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},containsEventTarget:function(){return!0},deregisterDocumentInteractionHandler:function(){},deregisterInteractionHandler:function(){},deregisterResizeHandler:function(){},getWindowPageOffset:function(){return{x:0,y:0}},isSurfaceActive:function(){return!0},isSurfaceDisabled:function(){return!0},isUnbounded:function(){return!0},registerDocumentInteractionHandler:function(){},registerInteractionHandler:function(){},registerResizeHandler:function(){},removeClass:function(){},updateCssVariable:function(){}}},enumerable:!1,configurable:!0}),l.prototype.init=function(){var t=this,e=this.supportsPressRipple();if(this.registerRootHandlers(e),e){var a=l.cssClasses,i=a.ROOT,r=a.UNBOUNDED;requestAnimationFrame((function(){t.adapter.addClass(i),t.adapter.isUnbounded()&&(t.adapter.addClass(r),t.layoutInternal())}))}},l.prototype.destroy=function(){var t=this;if(this.supportsPressRipple()){this.activationTimer&&(clearTimeout(this.activationTimer),this.activationTimer=0,this.adapter.removeClass(l.cssClasses.FG_ACTIVATION)),this.fgDeactivationRemovalTimer&&(clearTimeout(this.fgDeactivationRemovalTimer),this.fgDeactivationRemovalTimer=0,this.adapter.removeClass(l.cssClasses.FG_DEACTIVATION));var e=l.cssClasses,a=e.ROOT,i=e.UNBOUNDED;requestAnimationFrame((function(){t.adapter.removeClass(a),t.adapter.removeClass(i),t.removeCssVars()}))}this.deregisterRootHandlers(),this.deregisterDeactivationHandlers()},l.prototype.activate=function(t){this.activateImpl(t)},l.prototype.deactivate=function(){this.deactivateImpl()},l.prototype.layout=function(){var t=this;this.layoutFrame&&cancelAnimationFrame(this.layoutFrame),this.layoutFrame=requestAnimationFrame((function(){t.layoutInternal(),t.layoutFrame=0}))},l.prototype.setUnbounded=function(t){var e=l.cssClasses.UNBOUNDED;t?this.adapter.addClass(e):this.adapter.removeClass(e)},l.prototype.handleFocus=function(){var t=this;requestAnimationFrame((function(){return t.adapter.addClass(l.cssClasses.BG_FOCUSED)}))},l.prototype.handleBlur=function(){var t=this;requestAnimationFrame((function(){return t.adapter.removeClass(l.cssClasses.BG_FOCUSED)}))},l.prototype.supportsPressRipple=function(){return this.adapter.browserSupportsCssVars()},l.prototype.defaultActivationState=function(){return{activationEvent:void 0,hasDeactivationUXRun:!1,isActivated:!1,isProgrammatic:!1,wasActivatedByPointer:!1,wasElementMadeActive:!1}},l.prototype.registerRootHandlers=function(t){var e,i;if(t){try{for(var r=a(d),n=r.next();!n.done;n=r.next()){var s=n.value;this.adapter.registerInteractionHandler(s,this.activateHandler)}}catch(t){e={error:t}}finally{try{n&&!n.done&&(i=r.return)&&i.call(r)}finally{if(e)throw e.error}}this.adapter.isUnbounded()&&this.adapter.registerResizeHandler(this.resizeHandler)}this.adapter.registerInteractionHandler("focus",this.focusHandler),this.adapter.registerInteractionHandler("blur",this.blurHandler)},l.prototype.registerDeactivationHandlers=function(t){var e,i;if("keydown"===t.type)this.adapter.registerInteractionHandler("keyup",this.deactivateHandler);else try{for(var r=a(c),n=r.next();!n.done;n=r.next()){var s=n.value;this.adapter.registerDocumentInteractionHandler(s,this.deactivateHandler)}}catch(t){e={error:t}}finally{try{n&&!n.done&&(i=r.return)&&i.call(r)}finally{if(e)throw e.error}}},l.prototype.deregisterRootHandlers=function(){var t,e;try{for(var i=a(d),r=i.next();!r.done;r=i.next()){var n=r.value;this.adapter.deregisterInteractionHandler(n,this.activateHandler)}}catch(e){t={error:e}}finally{try{r&&!r.done&&(e=i.return)&&e.call(i)}finally{if(t)throw t.error}}this.adapter.deregisterInteractionHandler("focus",this.focusHandler),this.adapter.deregisterInteractionHandler("blur",this.blurHandler),this.adapter.isUnbounded()&&this.adapter.deregisterResizeHandler(this.resizeHandler)},l.prototype.deregisterDeactivationHandlers=function(){var t,e;this.adapter.deregisterInteractionHandler("keyup",this.deactivateHandler);try{for(var i=a(c),r=i.next();!r.done;r=i.next()){var n=r.value;this.adapter.deregisterDocumentInteractionHandler(n,this.deactivateHandler)}}catch(e){t={error:e}}finally{try{r&&!r.done&&(e=i.return)&&e.call(i)}finally{if(t)throw t.error}}},l.prototype.removeCssVars=function(){var t=this,e=l.strings;Object.keys(e).forEach((function(a){0===a.indexOf("VAR_")&&t.adapter.updateCssVariable(e[a],null)}))},l.prototype.activateImpl=function(t){var e=this;if(!this.adapter.isSurfaceDisabled()){var a=this.activationState;if(!a.isActivated){var i=this.previousActivationEvent;if(!(i&&void 0!==t&&i.type!==t.type))a.isActivated=!0,a.isProgrammatic=void 0===t,a.activationEvent=t,a.wasActivatedByPointer=!a.isProgrammatic&&(void 0!==t&&("mousedown"===t.type||"touchstart"===t.type||"pointerdown"===t.type)),void 0!==t&&u.length>0&&u.some((function(t){return e.adapter.containsEventTarget(t)}))?this.resetActivationState():(void 0!==t&&(u.push(t.target),this.registerDeactivationHandlers(t)),a.wasElementMadeActive=this.checkElementMadeActive(t),a.wasElementMadeActive&&this.animateActivation(),requestAnimationFrame((function(){u=[],a.wasElementMadeActive||void 0===t||" "!==t.key&&32!==t.keyCode||(a.wasElementMadeActive=e.checkElementMadeActive(t),a.wasElementMadeActive&&e.animateActivation()),a.wasElementMadeActive||(e.activationState=e.defaultActivationState())})))}}},l.prototype.checkElementMadeActive=function(t){return void 0===t||"keydown"!==t.type||this.adapter.isSurfaceActive()},l.prototype.animateActivation=function(){var t=this,e=l.strings,a=e.VAR_FG_TRANSLATE_START,i=e.VAR_FG_TRANSLATE_END,r=l.cssClasses,n=r.FG_DEACTIVATION,s=r.FG_ACTIVATION,o=l.numbers.DEACTIVATION_TIMEOUT_MS;this.layoutInternal();var d="",c="";if(!this.adapter.isUnbounded()){var u=this.getFgTranslationCoordinates(),h=u.startPoint,v=u.endPoint;d=h.x+"px, "+h.y+"px",c=v.x+"px, "+v.y+"px"}this.adapter.updateCssVariable(a,d),this.adapter.updateCssVariable(i,c),clearTimeout(this.activationTimer),clearTimeout(this.fgDeactivationRemovalTimer),this.rmBoundedActivationClasses(),this.adapter.removeClass(n),this.adapter.computeBoundingRect(),this.adapter.addClass(s),this.activationTimer=setTimeout((function(){t.activationTimerCallback()}),o)},l.prototype.getFgTranslationCoordinates=function(){var t,e=this.activationState,a=e.activationEvent;return{startPoint:t={x:(t=e.wasActivatedByPointer?o(a,this.adapter.getWindowPageOffset(),this.adapter.computeBoundingRect()):{x:this.frame.width/2,y:this.frame.height/2}).x-this.initialSize/2,y:t.y-this.initialSize/2},endPoint:{x:this.frame.width/2-this.initialSize/2,y:this.frame.height/2-this.initialSize/2}}},l.prototype.runDeactivationUXLogicIfReady=function(){var t=this,e=l.cssClasses.FG_DEACTIVATION,a=this.activationState,i=a.hasDeactivationUXRun,r=a.isActivated;(i||!r)&&this.activationAnimationHasEnded&&(this.rmBoundedActivationClasses(),this.adapter.addClass(e),this.fgDeactivationRemovalTimer=setTimeout((function(){t.adapter.removeClass(e)}),s.FG_DEACTIVATION_MS))},l.prototype.rmBoundedActivationClasses=function(){var t=l.cssClasses.FG_ACTIVATION;this.adapter.removeClass(t),this.activationAnimationHasEnded=!1,this.adapter.computeBoundingRect()},l.prototype.resetActivationState=function(){var t=this;this.previousActivationEvent=this.activationState.activationEvent,this.activationState=this.defaultActivationState(),setTimeout((function(){return t.previousActivationEvent=void 0}),l.numbers.TAP_DELAY_MS)},l.prototype.deactivateImpl=function(){var t=this,a=this.activationState;if(a.isActivated){var i=e({},a);a.isProgrammatic?(requestAnimationFrame((function(){t.animateDeactivation(i)})),this.resetActivationState()):(this.deregisterDeactivationHandlers(),requestAnimationFrame((function(){t.activationState.hasDeactivationUXRun=!0,t.animateDeactivation(i),t.resetActivationState()})))}},l.prototype.animateDeactivation=function(t){var e=t.wasActivatedByPointer,a=t.wasElementMadeActive;(e||a)&&this.runDeactivationUXLogicIfReady()},l.prototype.layoutInternal=function(){var t=this;this.frame=this.adapter.computeBoundingRect();var e=Math.max(this.frame.height,this.frame.width);this.maxRadius=this.adapter.isUnbounded()?e:Math.sqrt(Math.pow(t.frame.width,2)+Math.pow(t.frame.height,2))+l.numbers.PADDING;var a=Math.floor(e*l.numbers.INITIAL_ORIGIN_SCALE);this.adapter.isUnbounded()&&a%2!=0?this.initialSize=a-1:this.initialSize=a,this.fgScale=""+this.maxRadius/this.initialSize,this.updateLayoutCssVars()},l.prototype.updateLayoutCssVars=function(){var t=l.strings,e=t.VAR_FG_SIZE,a=t.VAR_LEFT,i=t.VAR_TOP,r=t.VAR_FG_SCALE;this.adapter.updateCssVariable(e,this.initialSize+"px"),this.adapter.updateCssVariable(r,this.fgScale),this.adapter.isUnbounded()&&(this.unboundedCoords={left:Math.round(this.frame.width/2-this.initialSize/2),top:Math.round(this.frame.height/2-this.initialSize/2)},this.adapter.updateCssVariable(a,this.unboundedCoords.left+"px"),this.adapter.updateCssVariable(i,this.unboundedCoords.top+"px"))},l}(i);export{l as MDCRippleFoundation,l as default};
//# sourceMappingURL=foundation.js.map
