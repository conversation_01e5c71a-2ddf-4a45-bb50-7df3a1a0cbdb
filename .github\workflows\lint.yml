name: <PERSON><PERSON> with Ruff

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]

jobs:
  ruff:
    environment: development
    runs-on: depot-ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install "ruff>0.1.7"
      - name: Run Ruff linting
        run: ruff check --output-format=github
