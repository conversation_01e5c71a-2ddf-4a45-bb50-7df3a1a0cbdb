[package]
name = "graph-visualizer-backend"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web framework
axum = { version = "0.7", features = ["ws"] }
tokio = { version = "1", features = ["full"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "fs", "compression-gzip", "compression-br"] }

# FalkorDB client
falkordb = { version = "0.1.10", features = ["tokio"] }

# DuckDB and Arrow
duckdb = { version = "1.3", features = ["bundled"] }
arrow = { version = "55.0", features = ["json", "csv", "ipc"] }
arrow-flight = "55.0"
arrow-schema = "55.0"
arrow-array = "55.0"
arrow-ipc = "55.0"
parquet = { version = "55.0", optional = true }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Date/Time
chrono = { version = "0.4", features = ["serde"] }

# Graph processing
petgraph = "0.6"
rayon = "1.7"

# Utilities
futures = "0.3"
dashmap = "5.5"
reqwest = { version = "0.11", features = ["json"] }
bytes = "1.5"
sha2 = "0.10"
base64 = "0.22"
uuid = { version = "1.17", features = ["v4"] }