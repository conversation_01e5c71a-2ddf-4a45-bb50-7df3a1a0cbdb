{"version": 3, "file": "index.js", "sources": ["../../../src/modules/search/index.ts"], "sourcesContent": ["import type { SvelteComponent } from 'svelte'\nimport type { AccessorOption, SearchData } from './types'\nimport { type SearchConfigInterface, type SearchEvents, defaultSearchConfig } from './config'\nimport { Events } from './types'\n\nimport SearchComponent from './search.svelte'\nimport { merge } from '../../utils'\n\nexport class Search<T extends SearchData> {\n  private _search: SvelteComponent\n  private _containerNode: HTMLElement\n  private _config: SearchConfigInterface<T> = {}\n\n  public constructor (containerNode: HTMLElement, config?: SearchConfigInterface<T>) {\n    this._containerNode = containerNode\n    this._config = merge(defaultSearchConfig, config ?? {})\n    this._search = new SearchComponent({ target: containerNode, props: { config: this._config } })\n    this._search.$on(Events.Input, ({ detail }) => this._config.events?.onSearch?.(detail))\n    this._search.$on(Events.Select, ({ detail }) => this._config.events?.onSelect?.(detail))\n    this._search.$on(Events.Enter, ({ detail }) => this._config.events?.onEnter?.(detail))\n    this._search.$on(Events.AccessorSelect, ({ detail }) => this._config.events?.onAccessorSelect?.(detail))\n  }\n\n  public setData (data: T[]): void {\n    this._search.$set({ data, textInput: '' })\n  }\n\n  public setConfig (config?: SearchConfigInterface<T>): void {\n    this._config = merge(defaultSearchConfig, config ?? {})\n    this._search.$set({ config: this._config, textInput: '' })\n  }\n\n  public setListState (state: boolean): void {\n    this._search.setListState(state)\n  }\n\n  public clearInput (): void {\n    this._search.$set({ textInput: '' })\n  }\n\n  /**  `getConfig`: Returns current `Search` configuration */\n  public getConfig (): SearchConfigInterface<T> {\n    return this._config\n  }\n\n  public destroy (): void {\n    this._containerNode.innerHTML = ''\n  }\n}\n\nexport type { SearchData, SearchConfigInterface, SearchEvents, AccessorOption as SearchAccessorOption }\n"], "names": ["Search", "constructor", "containerNode", "config", "this", "_config", "_containerNode", "merge", "defaultSearchConfig", "_search", "SearchComponent", "target", "props", "$on", "Events", "Input", "detail", "_a", "_b", "events", "onSearch", "call", "Select", "onSelect", "Enter", "onEnter", "AccessorSelect", "onAccessorSelect", "setData", "data", "$set", "textInput", "setConfig", "setListState", "state", "clearInput", "getConfig", "destroy", "innerHTML"], "mappings": "qKAQaA,EAKX,WAAAC,CAAoBC,EAA4BC,GAFxCC,KAAOC,QAA6B,GAG1CD,KAAKE,eAAiBJ,EACtBE,KAAKC,QAAUE,EAAMC,EAAqBL,QAAAA,EAAU,CAAE,GACtDC,KAAKK,QAAU,IAAIC,EAAgB,CAAEC,OAAQT,EAAeU,MAAO,CAAET,OAAQC,KAAKC,WAClFD,KAAKK,QAAQI,IAAIC,EAAOC,OAAO,EAAGC,aAAY,IAAAC,EAAAC,EAAC,OAA6B,QAA7BA,EAAmB,QAAnBD,EAAAb,KAAKC,QAAQc,cAAM,IAAAF,OAAA,EAAAA,EAAEG,gBAAQ,IAAAF,OAAA,EAAAA,EAAAG,KAAAJ,EAAGD,EAAO,IACtFZ,KAAKK,QAAQI,IAAIC,EAAOQ,QAAQ,EAAGN,aAAY,IAAAC,EAAAC,EAAC,OAA6B,QAA7BA,EAAmB,QAAnBD,EAAAb,KAAKC,QAAQc,cAAM,IAAAF,OAAA,EAAAA,EAAEM,gBAAQ,IAAAL,OAAA,EAAAA,EAAAG,KAAAJ,EAAGD,EAAO,IACvFZ,KAAKK,QAAQI,IAAIC,EAAOU,OAAO,EAAGR,aAAY,IAAAC,EAAAC,EAAC,OAA4B,QAA5BA,EAAmB,QAAnBD,EAAAb,KAAKC,QAAQc,cAAM,IAAAF,OAAA,EAAAA,EAAEQ,eAAO,IAAAP,OAAA,EAAAA,EAAAG,KAAAJ,EAAGD,EAAO,IACrFZ,KAAKK,QAAQI,IAAIC,EAAOY,gBAAgB,EAAGV,aAAY,IAAAC,EAAAC,EAAC,OAAqC,QAArCA,EAAmB,QAAnBD,EAAAb,KAAKC,QAAQc,cAAM,IAAAF,OAAA,EAAAA,EAAEU,wBAAgB,IAAAT,OAAA,EAAAA,EAAAG,KAAAJ,EAAGD,EAAO,GACxG,CAEM,OAAAY,CAASC,GACdzB,KAAKK,QAAQqB,KAAK,CAAED,OAAME,UAAW,IACtC,CAEM,SAAAC,CAAW7B,GAChBC,KAAKC,QAAUE,EAAMC,EAAqBL,QAAAA,EAAU,CAAE,GACtDC,KAAKK,QAAQqB,KAAK,CAAE3B,OAAQC,KAAKC,QAAS0B,UAAW,IACtD,CAEM,YAAAE,CAAcC,GACnB9B,KAAKK,QAAQwB,aAAaC,EAC3B,CAEM,UAAAC,GACL/B,KAAKK,QAAQqB,KAAK,CAAEC,UAAW,IAChC,CAGM,SAAAK,GACL,OAAOhC,KAAKC,OACb,CAEM,OAAAgC,GACLjC,KAAKE,eAAegC,UAAY,EACjC"}