{"version": 3, "file": "constants.js", "sources": ["../../../../../../node_modules/@material/ripple/constants.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nexport var cssClasses = {\n    // Ripple is a special case where the \"root\" component is really a \"mixin\" of sorts,\n    // given that it's an 'upgrade' to an existing component. That being said it is the root\n    // CSS class that all other CSS classes derive from.\n    BG_FOCUSED: 'mdc-ripple-upgraded--background-focused',\n    FG_ACTIVATION: 'mdc-ripple-upgraded--foreground-activation',\n    FG_DEACTIVATION: 'mdc-ripple-upgraded--foreground-deactivation',\n    ROOT: 'mdc-ripple-upgraded',\n    UNBOUNDED: 'mdc-ripple-upgraded--unbounded',\n};\nexport var strings = {\n    VAR_FG_SCALE: '--mdc-ripple-fg-scale',\n    VAR_FG_SIZE: '--mdc-ripple-fg-size',\n    VAR_FG_TRANSLATE_END: '--mdc-ripple-fg-translate-end',\n    VAR_FG_TRANSLATE_START: '--mdc-ripple-fg-translate-start',\n    VAR_LEFT: '--mdc-ripple-left',\n    VAR_TOP: '--mdc-ripple-top',\n};\nexport var numbers = {\n    DEACTIVATION_TIMEOUT_MS: 225,\n    FG_DEACTIVATION_MS: 150,\n    INITIAL_ORIGIN_SCALE: 0.6,\n    PADDING: 10,\n    TAP_DELAY_MS: 300, // Delay between touch and simulated mouse events on touch devices\n};\n//# sourceMappingURL=constants.js.map"], "names": ["cssClasses", "BG_FOCUSED", "FG_ACTIVATION", "FG_DEACTIVATION", "ROOT", "UNBOUNDED", "strings", "VAR_FG_SCALE", "VAR_FG_SIZE", "VAR_FG_TRANSLATE_END", "VAR_FG_TRANSLATE_START", "VAR_LEFT", "VAR_TOP", "numbers", "DEACTIVATION_TIMEOUT_MS", "FG_DEACTIVATION_MS", "INITIAL_ORIGIN_SCALE", "PADDING", "TAP_DELAY_MS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAsBU,IAACA,EAAa,CAIpBC,WAAY,0CACZC,cAAe,6CACfC,gBAAiB,+CACjBC,KAAM,sBACNC,UAAW,kCAEJC,EAAU,CACjBC,aAAc,wBACdC,YAAa,uBACbC,qBAAsB,gCACtBC,uBAAwB,kCACxBC,SAAU,oBACVC,QAAS,oBAEFC,EAAU,CACjBC,wBAAyB,IACzBC,mBAAoB,IACpBC,qBAAsB,GACtBC,QAAS,GACTC,aAAc"}