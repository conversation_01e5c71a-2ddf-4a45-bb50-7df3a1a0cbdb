import{__extends as e,__assign as t,__read as i,__spreadArray as s}from"../../../_virtual/_tslib.js";import{MDCFoundation as n}from"../base/foundation.js";import{normalizeKey as r}from"../dom/keyboard.js";import{numbers as o,strings as d,cssClasses as a}from"./constants.js";import{preventDefaultEvent as h}from"./events.js";import{initState as c,isTypingInProgress as l,handleKeydown as I,matchItem as u,initSortedIndex as x,clearBuffer as f}from"./typeahead.js";
/**
 * @license
 * Copyright 2018 Google Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */var p=["Alt","Control","Meta","Shift"];function A(e){var t=new Set(e?p.filter((function(t){return e.getModifierState(t)})):[]);return function(e){return e.every((function(e){return t.has(e)}))&&e.length===t.size}}var m=function(n){function p(e){var i=n.call(this,t(t({},p.defaultAdapter),e))||this;return i.wrapFocus=!1,i.isVertical=!0,i.isSingleSelectionList=!1,i.areDisabledItemsFocusable=!0,i.selectedIndex=o.UNSET_INDEX,i.focusedItemIndex=o.UNSET_INDEX,i.useActivatedClass=!1,i.useSelectedAttr=!1,i.ariaCurrentAttrValue=null,i.isCheckboxList=!1,i.isRadioList=!1,i.lastSelectedIndex=null,i.hasTypeahead=!1,i.typeaheadState=c(),i.sortedIndexByFirstChar=new Map,i}return e(p,n),Object.defineProperty(p,"strings",{get:function(){return d},enumerable:!1,configurable:!0}),Object.defineProperty(p,"cssClasses",{get:function(){return a},enumerable:!1,configurable:!0}),Object.defineProperty(p,"numbers",{get:function(){return o},enumerable:!1,configurable:!0}),Object.defineProperty(p,"defaultAdapter",{get:function(){return{addClassForElementIndex:function(){},focusItemAtIndex:function(){},getAttributeForElementIndex:function(){return null},getFocusedElementIndex:function(){return 0},getListItemCount:function(){return 0},hasCheckboxAtIndex:function(){return!1},hasRadioAtIndex:function(){return!1},isCheckboxCheckedAtIndex:function(){return!1},isFocusInsideList:function(){return!1},isRootFocused:function(){return!1},listItemAtIndexHasClass:function(){return!1},notifyAction:function(){},notifySelectionChange:function(){},removeClassForElementIndex:function(){},setAttributeForElementIndex:function(){},setCheckedCheckboxOrRadioAtIndex:function(){},setTabIndexForListItemChildren:function(){},getPrimaryTextAtIndex:function(){return""}}},enumerable:!1,configurable:!0}),p.prototype.layout=function(){0!==this.adapter.getListItemCount()&&(this.adapter.hasCheckboxAtIndex(0)?this.isCheckboxList=!0:this.adapter.hasRadioAtIndex(0)?this.isRadioList=!0:this.maybeInitializeSingleSelection(),this.hasTypeahead&&(this.sortedIndexByFirstChar=this.typeaheadInitSortedIndex()))},p.prototype.getFocusedItemIndex=function(){return this.focusedItemIndex},p.prototype.setWrapFocus=function(e){this.wrapFocus=e},p.prototype.setVerticalOrientation=function(e){this.isVertical=e},p.prototype.setSingleSelection=function(e){this.isSingleSelectionList=e,e&&(this.maybeInitializeSingleSelection(),this.selectedIndex=this.getSelectedIndexFromDOM())},p.prototype.setDisabledItemsFocusable=function(e){this.areDisabledItemsFocusable=e},p.prototype.maybeInitializeSingleSelection=function(){var e=this.getSelectedIndexFromDOM();e!==o.UNSET_INDEX&&(this.adapter.listItemAtIndexHasClass(e,a.LIST_ITEM_ACTIVATED_CLASS)&&this.setUseActivatedClass(!0),this.isSingleSelectionList=!0,this.selectedIndex=e)},p.prototype.getSelectedIndexFromDOM=function(){for(var e=o.UNSET_INDEX,t=this.adapter.getListItemCount(),i=0;i<t;i++){var s=this.adapter.listItemAtIndexHasClass(i,a.LIST_ITEM_SELECTED_CLASS),n=this.adapter.listItemAtIndexHasClass(i,a.LIST_ITEM_ACTIVATED_CLASS);if(s||n){e=i;break}}return e},p.prototype.setHasTypeahead=function(e){this.hasTypeahead=e,e&&(this.sortedIndexByFirstChar=this.typeaheadInitSortedIndex())},p.prototype.isTypeaheadInProgress=function(){return this.hasTypeahead&&l(this.typeaheadState)},p.prototype.setUseActivatedClass=function(e){this.useActivatedClass=e},p.prototype.setUseSelectedAttribute=function(e){this.useSelectedAttr=e},p.prototype.getSelectedIndex=function(){return this.selectedIndex},p.prototype.setSelectedIndex=function(e,t){void 0===t&&(t={}),this.isIndexValid(e)&&(this.isCheckboxList?this.setCheckboxAtIndex(e,t):this.isRadioList?this.setRadioAtIndex(e,t):this.setSingleSelectionAtIndex(e,t))},p.prototype.handleFocusIn=function(e){e>=0&&(this.focusedItemIndex=e,this.adapter.setAttributeForElementIndex(e,"tabindex","0"),this.adapter.setTabIndexForListItemChildren(e,"0"))},p.prototype.handleFocusOut=function(e){var t=this;e>=0&&(this.adapter.setAttributeForElementIndex(e,"tabindex","-1"),this.adapter.setTabIndexForListItemChildren(e,"-1")),setTimeout((function(){t.adapter.isFocusInsideList()||t.setTabindexToFirstSelectedOrFocusedItem()}),0)},p.prototype.isIndexDisabled=function(e){return this.adapter.listItemAtIndexHasClass(e,a.LIST_ITEM_DISABLED_CLASS)},p.prototype.handleKeydown=function(e,t,i){var s,n=this,d="ArrowLeft"===r(e),a="ArrowUp"===r(e),c="ArrowRight"===r(e),l="ArrowDown"===r(e),u="Home"===r(e),x="End"===r(e),f="Enter"===r(e),p="Spacebar"===r(e),m=this.isVertical&&l||!this.isVertical&&c,S=this.isVertical&&a||!this.isVertical&&d,b="A"===e.key||"a"===e.key,E=A(e);if(this.adapter.isRootFocused()){if((S||x)&&E([]))e.preventDefault(),this.focusLastElement();else if((m||u)&&E([]))e.preventDefault(),this.focusFirstElement();else if(S&&E(["Shift"])&&this.isCheckboxList){e.preventDefault(),-1!==(g=this.focusLastElement())&&this.setSelectedIndexOnAction(g,!1)}else if(m&&E(["Shift"])&&this.isCheckboxList){e.preventDefault(),-1!==(g=this.focusFirstElement())&&this.setSelectedIndexOnAction(g,!1)}if(this.hasTypeahead){var C={event:e,focusItemAtIndex:function(e){n.focusItemAtIndex(e)},focusedItemIndex:-1,isTargetListItem:t,sortedIndexByFirstChar:this.sortedIndexByFirstChar,isItemAtIndexDisabled:function(e){return n.isIndexDisabled(e)}};I(C,this.typeaheadState)}}else{var y=this.adapter.getFocusedElementIndex();if(!(-1===y&&(y=i)<0)){if(m&&E([]))h(e),this.focusNextElement(y);else if(S&&E([]))h(e),this.focusPrevElement(y);else if(m&&E(["Shift"])&&this.isCheckboxList){h(e),-1!==(g=this.focusNextElement(y))&&this.setSelectedIndexOnAction(g,!1)}else if(S&&E(["Shift"])&&this.isCheckboxList){var g;h(e),-1!==(g=this.focusPrevElement(y))&&this.setSelectedIndexOnAction(g,!1)}else if(u&&E([]))h(e),this.focusFirstElement();else if(x&&E([]))h(e),this.focusLastElement();else if(u&&E(["Control","Shift"])&&this.isCheckboxList){if(h(e),this.isIndexDisabled(y))return;this.focusFirstElement(),this.toggleCheckboxRange(0,y,y)}else if(x&&E(["Control","Shift"])&&this.isCheckboxList){if(h(e),this.isIndexDisabled(y))return;this.focusLastElement(),this.toggleCheckboxRange(y,this.adapter.getListItemCount()-1,y)}else if(b&&E(["Control"])&&this.isCheckboxList)e.preventDefault(),this.checkboxListToggleAll(this.selectedIndex===o.UNSET_INDEX?[]:this.selectedIndex,!0);else if((f||p)&&E([])){if(t){if((L=e.target)&&"A"===L.tagName&&f)return;if(h(e),this.isIndexDisabled(y))return;this.isTypeaheadInProgress()||(this.isSelectableList()&&this.setSelectedIndexOnAction(y,!1),this.adapter.notifyAction(y))}}else if((f||p)&&E(["Shift"])&&this.isCheckboxList){var L;if((L=e.target)&&"A"===L.tagName&&f)return;if(h(e),this.isIndexDisabled(y))return;this.isTypeaheadInProgress()||(this.toggleCheckboxRange(null!==(s=this.lastSelectedIndex)&&void 0!==s?s:y,y,y),this.adapter.notifyAction(y))}if(this.hasTypeahead){C={event:e,focusItemAtIndex:function(e){n.focusItemAtIndex(e)},focusedItemIndex:this.focusedItemIndex,isTargetListItem:t,sortedIndexByFirstChar:this.sortedIndexByFirstChar,isItemAtIndexDisabled:function(e){return n.isIndexDisabled(e)}};I(C,this.typeaheadState)}}}},p.prototype.handleClick=function(e,t,i){var s,n=A(i);e!==o.UNSET_INDEX&&(this.isIndexDisabled(e)||(n([])?(this.isSelectableList()&&this.setSelectedIndexOnAction(e,t),this.adapter.notifyAction(e)):this.isCheckboxList&&n(["Shift"])&&(this.toggleCheckboxRange(null!==(s=this.lastSelectedIndex)&&void 0!==s?s:e,e,e),this.adapter.notifyAction(e))))},p.prototype.focusNextElement=function(e){var t=this.adapter.getListItemCount(),i=e,s=null;do{if(++i>=t){if(!this.wrapFocus)return e;i=0}if(i===s)return-1;s=null!=s?s:i}while(!this.areDisabledItemsFocusable&&this.isIndexDisabled(i));return this.focusItemAtIndex(i),i},p.prototype.focusPrevElement=function(e){var t=this.adapter.getListItemCount(),i=e,s=null;do{if(--i<0){if(!this.wrapFocus)return e;i=t-1}if(i===s)return-1;s=null!=s?s:i}while(!this.areDisabledItemsFocusable&&this.isIndexDisabled(i));return this.focusItemAtIndex(i),i},p.prototype.focusFirstElement=function(){return this.focusNextElement(-1)},p.prototype.focusLastElement=function(){return this.focusPrevElement(this.adapter.getListItemCount())},p.prototype.focusInitialElement=function(){var e=this.getFirstSelectedOrFocusedItemIndex();return this.focusItemAtIndex(e),e},p.prototype.setEnabled=function(e,t){this.isIndexValid(e,!1)&&(t?(this.adapter.removeClassForElementIndex(e,a.LIST_ITEM_DISABLED_CLASS),this.adapter.setAttributeForElementIndex(e,d.ARIA_DISABLED,"false")):(this.adapter.addClassForElementIndex(e,a.LIST_ITEM_DISABLED_CLASS),this.adapter.setAttributeForElementIndex(e,d.ARIA_DISABLED,"true")))},p.prototype.setSingleSelectionAtIndex=function(e,t){if(void 0===t&&(t={}),this.selectedIndex!==e||t.forceUpdate){var i=a.LIST_ITEM_SELECTED_CLASS;this.useActivatedClass&&(i=a.LIST_ITEM_ACTIVATED_CLASS),this.selectedIndex!==o.UNSET_INDEX&&this.adapter.removeClassForElementIndex(this.selectedIndex,i),this.setAriaForSingleSelectionAtIndex(e),this.setTabindexAtIndex(e),e!==o.UNSET_INDEX&&this.adapter.addClassForElementIndex(e,i),this.selectedIndex=e,t.isUserInteraction&&!t.forceUpdate&&this.adapter.notifySelectionChange([e])}},p.prototype.setAriaForSingleSelectionAtIndex=function(e){this.selectedIndex===o.UNSET_INDEX&&(this.ariaCurrentAttrValue=this.adapter.getAttributeForElementIndex(e,d.ARIA_CURRENT));var t=null!==this.ariaCurrentAttrValue,i=t?d.ARIA_CURRENT:d.ARIA_SELECTED;if(this.selectedIndex!==o.UNSET_INDEX&&this.adapter.setAttributeForElementIndex(this.selectedIndex,i,"false"),e!==o.UNSET_INDEX){var s=t?this.ariaCurrentAttrValue:"true";this.adapter.setAttributeForElementIndex(e,i,s)}},p.prototype.getSelectionAttribute=function(){return this.useSelectedAttr?d.ARIA_SELECTED:d.ARIA_CHECKED},p.prototype.setRadioAtIndex=function(e,t){void 0===t&&(t={});var i=this.getSelectionAttribute();this.adapter.setCheckedCheckboxOrRadioAtIndex(e,!0),(this.selectedIndex!==e||t.forceUpdate)&&(this.selectedIndex!==o.UNSET_INDEX&&this.adapter.setAttributeForElementIndex(this.selectedIndex,i,"false"),this.adapter.setAttributeForElementIndex(e,i,"true"),this.selectedIndex=e,t.isUserInteraction&&!t.forceUpdate&&this.adapter.notifySelectionChange([e]))},p.prototype.setCheckboxAtIndex=function(e,t){void 0===t&&(t={});for(var i=this.selectedIndex,s=t.isUserInteraction?new Set(i===o.UNSET_INDEX?[]:i):null,n=this.getSelectionAttribute(),r=[],d=0;d<this.adapter.getListItemCount();d++){var a=null==s?void 0:s.has(d),h=e.indexOf(d)>=0;h!==a&&r.push(d),this.adapter.setCheckedCheckboxOrRadioAtIndex(d,h),this.adapter.setAttributeForElementIndex(d,n,h?"true":"false")}this.selectedIndex=e,t.isUserInteraction&&r.length&&this.adapter.notifySelectionChange(r)},p.prototype.toggleCheckboxRange=function(e,t,n){this.lastSelectedIndex=n;for(var r=new Set(this.selectedIndex===o.UNSET_INDEX?[]:this.selectedIndex),d=!(null==r?void 0:r.has(n)),a=i([e,t].sort(),2),h=a[0],c=a[1],l=this.getSelectionAttribute(),I=[],u=h;u<=c;u++){if(!this.isIndexDisabled(u))d!==r.has(u)&&(I.push(u),this.adapter.setCheckedCheckboxOrRadioAtIndex(u,d),this.adapter.setAttributeForElementIndex(u,l,""+d),d?r.add(u):r.delete(u))}I.length&&(this.selectedIndex=s([],i(r)),this.adapter.notifySelectionChange(I))},p.prototype.setTabindexAtIndex=function(e){this.focusedItemIndex===o.UNSET_INDEX&&0!==e?this.adapter.setAttributeForElementIndex(0,"tabindex","-1"):this.focusedItemIndex>=0&&this.focusedItemIndex!==e&&this.adapter.setAttributeForElementIndex(this.focusedItemIndex,"tabindex","-1"),this.selectedIndex instanceof Array||this.selectedIndex===e||this.adapter.setAttributeForElementIndex(this.selectedIndex,"tabindex","-1"),e!==o.UNSET_INDEX&&this.adapter.setAttributeForElementIndex(e,"tabindex","0")},p.prototype.isSelectableList=function(){return this.isSingleSelectionList||this.isCheckboxList||this.isRadioList},p.prototype.setTabindexToFirstSelectedOrFocusedItem=function(){var e=this.getFirstSelectedOrFocusedItemIndex();this.setTabindexAtIndex(e)},p.prototype.getFirstSelectedOrFocusedItemIndex=function(){return this.isSelectableList()?"number"==typeof this.selectedIndex&&this.selectedIndex!==o.UNSET_INDEX?this.selectedIndex:this.selectedIndex instanceof Array&&this.selectedIndex.length>0?this.selectedIndex.reduce((function(e,t){return Math.min(e,t)})):0:Math.max(this.focusedItemIndex,0)},p.prototype.isIndexValid=function(e,t){var i=this;if(void 0===t&&(t=!0),e instanceof Array){if(!this.isCheckboxList&&t)throw new Error("MDCListFoundation: Array of index is only supported for checkbox based list");return 0===e.length||e.some((function(e){return i.isIndexInRange(e)}))}if("number"==typeof e){if(this.isCheckboxList&&t)throw new Error("MDCListFoundation: Expected array of index for checkbox based list but got number: "+e);return this.isIndexInRange(e)||this.isSingleSelectionList&&e===o.UNSET_INDEX}return!1},p.prototype.isIndexInRange=function(e){var t=this.adapter.getListItemCount();return e>=0&&e<t},p.prototype.setSelectedIndexOnAction=function(e,t){this.lastSelectedIndex=e,this.isCheckboxList?(this.toggleCheckboxAtIndex(e,t),this.adapter.notifySelectionChange([e])):this.setSelectedIndex(e,{isUserInteraction:!0})},p.prototype.toggleCheckboxAtIndex=function(e,t){var i,s=this.getSelectionAttribute(),n=this.adapter.isCheckboxCheckedAtIndex(e);t?i=n:(i=!n,this.adapter.setCheckedCheckboxOrRadioAtIndex(e,i)),this.adapter.setAttributeForElementIndex(e,s,i?"true":"false");var r=this.selectedIndex===o.UNSET_INDEX?[]:this.selectedIndex.slice();i?r.push(e):r=r.filter((function(t){return t!==e})),this.selectedIndex=r},p.prototype.focusItemAtIndex=function(e){this.adapter.focusItemAtIndex(e),this.focusedItemIndex=e},p.prototype.checkboxListToggleAll=function(e,t){var i=this.adapter.getListItemCount();if(e.length===i)this.setCheckboxAtIndex([],{isUserInteraction:t});else{for(var s=[],n=0;n<i;n++)(!this.isIndexDisabled(n)||e.indexOf(n)>-1)&&s.push(n);this.setCheckboxAtIndex(s,{isUserInteraction:t})}},p.prototype.typeaheadMatchItem=function(e,t,i){var s=this;void 0===i&&(i=!1);var n={focusItemAtIndex:function(e){s.focusItemAtIndex(e)},focusedItemIndex:t||this.focusedItemIndex,nextChar:e,sortedIndexByFirstChar:this.sortedIndexByFirstChar,skipFocus:i,isItemAtIndexDisabled:function(e){return s.isIndexDisabled(e)}};return u(n,this.typeaheadState)},p.prototype.typeaheadInitSortedIndex=function(){return x(this.adapter.getListItemCount(),this.adapter.getPrimaryTextAtIndex)},p.prototype.clearTypeaheadBuffer=function(){f(this.typeaheadState)},p}(n);export{m as MDCListFoundation,m as default};
//# sourceMappingURL=foundation.js.map
