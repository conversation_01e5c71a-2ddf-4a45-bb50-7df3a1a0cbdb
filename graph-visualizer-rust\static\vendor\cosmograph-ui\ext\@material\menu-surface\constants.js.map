{"version": 3, "file": "constants.js", "sources": ["../../../../../../node_modules/@material/menu-surface/constants.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar cssClasses = {\n    ANCHOR: 'mdc-menu-surface--anchor',\n    ANIMATING_CLOSED: 'mdc-menu-surface--animating-closed',\n    ANIMATING_OPEN: 'mdc-menu-surface--animating-open',\n    FIXED: 'mdc-menu-surface--fixed',\n    IS_OPEN_BELOW: 'mdc-menu-surface--is-open-below',\n    OPEN: 'mdc-menu-surface--open',\n    ROOT: 'mdc-menu-surface',\n};\n// tslint:disable:object-literal-sort-keys\nvar strings = {\n    CLOSED_EVENT: 'MDCMenuSurface:closed',\n    CLOSING_EVENT: 'MDCMenuSurface:closing',\n    OPENED_EVENT: 'MDCMenuSurface:opened',\n    OPENING_EVENT: 'MDCMenuSurface:opening',\n    FOCUSABLE_ELEMENTS: [\n        'button:not(:disabled)',\n        '[href]:not([aria-disabled=\"true\"])',\n        'input:not(:disabled)',\n        'select:not(:disabled)',\n        'textarea:not(:disabled)',\n        '[tabindex]:not([tabindex=\"-1\"]):not([aria-disabled=\"true\"])',\n    ].join(', '),\n};\n// tslint:enable:object-literal-sort-keys\nvar numbers = {\n    /** Total duration of menu-surface open animation. */\n    TRANSITION_OPEN_DURATION: 120,\n    /** Total duration of menu-surface close animation. */\n    TRANSITION_CLOSE_DURATION: 75,\n    /**\n     * Margin left to the edge of the viewport when menu-surface is at maximum\n     * possible height. Also used as a viewport margin.\n     */\n    MARGIN_TO_EDGE: 32,\n    /**\n     * Ratio of anchor width to menu-surface width for switching from corner\n     * positioning to center positioning.\n     */\n    ANCHOR_TO_MENU_SURFACE_WIDTH_RATIO: 0.67,\n    /**\n     * Amount of time to wait before restoring focus when closing the menu\n     * surface. This is important because if a touch event triggered the menu\n     * close, and the subsequent mouse event occurs after focus is restored, then\n     * the restored focus would be lost.\n     */\n    TOUCH_EVENT_WAIT_MS: 30,\n};\n/**\n * Enum for bits in the {@see Corner) bitmap.\n */\nvar CornerBit;\n(function (CornerBit) {\n    CornerBit[CornerBit[\"BOTTOM\"] = 1] = \"BOTTOM\";\n    CornerBit[CornerBit[\"CENTER\"] = 2] = \"CENTER\";\n    CornerBit[CornerBit[\"RIGHT\"] = 4] = \"RIGHT\";\n    CornerBit[CornerBit[\"FLIP_RTL\"] = 8] = \"FLIP_RTL\";\n})(CornerBit || (CornerBit = {}));\n/**\n * Enum for representing an element corner for positioning the menu-surface.\n *\n * The START constants map to LEFT if element directionality is left\n * to right and RIGHT if the directionality is right to left.\n * Likewise END maps to RIGHT or LEFT depending on the directionality.\n */\nvar Corner;\n(function (Corner) {\n    Corner[Corner[\"TOP_LEFT\"] = 0] = \"TOP_LEFT\";\n    Corner[Corner[\"TOP_RIGHT\"] = 4] = \"TOP_RIGHT\";\n    Corner[Corner[\"BOTTOM_LEFT\"] = 1] = \"BOTTOM_LEFT\";\n    Corner[Corner[\"BOTTOM_RIGHT\"] = 5] = \"BOTTOM_RIGHT\";\n    Corner[Corner[\"TOP_START\"] = 8] = \"TOP_START\";\n    Corner[Corner[\"TOP_END\"] = 12] = \"TOP_END\";\n    Corner[Corner[\"BOTTOM_START\"] = 9] = \"BOTTOM_START\";\n    Corner[Corner[\"BOTTOM_END\"] = 13] = \"BOTTOM_END\";\n})(Corner || (Corner = {}));\nexport { cssClasses, strings, numbers, CornerBit, Corner };\n//# sourceMappingURL=constants.js.map"], "names": ["CornerBit", "Corner", "cssClasses", "ANCHOR", "ANIMATING_CLOSED", "ANIMATING_OPEN", "FIXED", "IS_OPEN_BELOW", "OPEN", "ROOT", "strings", "CLOSED_EVENT", "CLOSING_EVENT", "OPENED_EVENT", "OPENING_EVENT", "FOCUSABLE_ELEMENTS", "join", "numbers", "TRANSITION_OPEN_DURATION", "TRANSITION_CLOSE_DURATION", "MARGIN_TO_EDGE", "ANCHOR_TO_MENU_SURFACE_WIDTH_RATIO", "TOUCH_EVENT_WAIT_MS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAsBG,IAmDCA,EAcAC,EAjEAC,EAAa,CACbC,OAAQ,2BACRC,iBAAkB,qCAClBC,eAAgB,mCAChBC,MAAO,0BACPC,cAAe,kCACfC,KAAM,yBACNC,KAAM,oBAGNC,EAAU,CACVC,aAAc,wBACdC,cAAe,yBACfC,aAAc,wBACdC,cAAe,yBACfC,mBAAoB,CAChB,wBACA,qCACA,uBACA,wBACA,0BACA,+DACFC,KAAK,OAGPC,EAAU,CAEVC,yBAA0B,IAE1BC,0BAA2B,GAK3BC,eAAgB,GAKhBC,mCAAoC,IAOpCC,oBAAqB,KAMzB,SAAWtB,GACPA,EAAUA,EAAkB,OAAI,GAAK,SACrCA,EAAUA,EAAkB,OAAI,GAAK,SACrCA,EAAUA,EAAiB,MAAI,GAAK,QACpCA,EAAUA,EAAoB,SAAI,GAAK,UAC1C,CALD,CAKGA,IAAcA,EAAY,CAAE,IAS/B,SAAWC,GACPA,EAAOA,EAAiB,SAAI,GAAK,WACjCA,EAAOA,EAAkB,UAAI,GAAK,YAClCA,EAAOA,EAAoB,YAAI,GAAK,cACpCA,EAAOA,EAAqB,aAAI,GAAK,eACrCA,EAAOA,EAAkB,UAAI,GAAK,YAClCA,EAAOA,EAAgB,QAAI,IAAM,UACjCA,EAAOA,EAAqB,aAAI,GAAK,eACrCA,EAAOA,EAAmB,WAAI,IAAM,YACvC,CATD,CASGA,IAAWA,EAAS,CAAA"}