import { Meta } from "@storybook/blocks";

<Meta title="Configuration" />

# cosmos.gl configuration properties

| Property | Description | Default |
|---|---|---|
| enableSimulation | If set to `false`, the simulation will not run. This property will be applied only on component initialization and it can't be changed using the `setConfig` method | `true` |
| backgroundColor | Canvas background color | `#222222` |
| spaceSize | Simulation space size (max 8192) | `8192` |
| pointColor | The default color to use for points when no point colors are provided, or if the color value in the array is `undefined` or `null`. This can be either a hex color string (e.g., '#b3b3b3') or an array of RGBA values in the format `[red, green, blue, alpha]` where each value is a number between 0 and 255 | `#b3b3b3` |
| pointGreyoutOpacity | Greyed out point opacity value when the selection is active | `undefined` |
| pointGreyoutColor | Greyed out point color value when the selection is active | `undefined` |
| pointSize | The default size value to use for points when no point sizes are provided or if the size value in the array is `undefined` or `null` | `4` |
| pointOpacity | Universal opacity value applied to all points. This value multiplies with individual point alpha values (if set via setPointColors). Useful for dynamically controlling opacity of all points without updating individual RGBA arrays. | `1.0` |
| pointSizeScale | Scale factor for the point size | `1` |
| hoveredPointCursor | Cursor style to use when hovering over a point | `auto` |
| renderHoveredPointRing | Turns ring rendering around a point on hover on / off | `false` |
| hoveredPointRingColor | Hovered point ring color hex value or an array of RGBA values | `white` |
| focusedPointRingColor | Focused point ring color hex value or an array of RGBA values | `white` |
| focusedPointIndex | Set focus on a point by index. A ring will be highlighted around the focused point. When set to `undefined`, no point is focused. | `undefined` |
| renderLinks | Turns link rendering on / off | `true` |
| linkColor | The default color to use for links when no link colors are provided, or if the color value in the array is `undefined` or `null`. This can be either a hex color string (e.g., '#666666') or an array of RGBA values in the format `[red, green, blue, alpha]` where each value is a number between 0 and 255 | `#666666` |
| linkOpacity | Universal opacity value applied to all links. This value multiplies with individual link alpha values (if set via setLinkColors). Useful for dynamically controlling opacity of all links without updating individual RGBA arrays. | `1.0` |
| linkGreyoutOpacity | Greyed out link opacity value when the selection is active | `0.1` |
| linkWidth | The default width value to use for links when no link widths are provided or if the width value in the array is `undefined` or `null` | `1` |
| linkWidthScale | Scale factor for the link width | `1` |
| scaleLinksOnZoom | Increase/decrease link width when zooming | `false` |
| curvedLinks | If set to true, links are rendered as curved lines. Otherwise as straight lines | `false` |
| curvedLinkSegments | Number of segments in a curved line | `19` |
| curvedLinkWeight | Weight affects the shape of the curve | `0.8` |
| curvedLinkControlPointDistance | Defines the position of the control point of the curve on the normal from the centre of the line. If set to 1 then the control point is at a distance equal to the length of the line | `0.5` |
| linkArrows | The default link arrow value that controls whether or not to display link arrows | `false` |
| linkArrowsSizeScale | Scale factor for the link arrows size | `1` |
| linkVisibilityDistanceRange | The range defines the minimum and maximum link visibility distance in pixels.<br /><br />The link will be fully opaque when its length is less than the first number in the array, and will have `linkVisibilityMinTransparency` transparency when its length is greater than the second number in the array.<br /><br />This distance is defined in screen space coordinates and will change as you zoom in and out (e.g. links become longer when you zoom in, and shorter when you zoom out). | `[50, 150]` |
| linkVisibilityMinTransparency | The transparency value that the link will have when its length reaches the maximum link distance value from `linkVisibilityDistanceRange`. | `0.25` |
| useClassicQuadtree | Use the classic [quadtree algorithm](https://en.wikipedia.org/wiki/Barnes%E2%80%93Hut_simulation) for the Many-Body force. This property will be applied only on component initialization and it can't be changed using the `setConfig` method.<br /><br /> ⚠ The algorithm might not work on some GPUs (e.g. Nvidia) and on Windows (unless you disable ANGLE in the browser settings). | `false` |
| showFPSMonitor | Show WebGL performance monitor | `false` |
| pixelRatio | Canvas pixel ratio | `2` |
| scalePointsOnZoom | Increase/decrease point size when zooming | `false` |
| initialZoomLevel | Initial zoom level (set once during initialization) | `undefined` |
| enableZoom | Enables zooming interactions | `true` |
| enableSimulationDuringZoom | Keep simulation running during zoom operations | `false` |
| enableDrag | Enable/disable point dragging | `false` |
| fitViewOnInit | Auto-zoom to fit all points on initialization | `true` |
| fitViewDelay | Delay before fitting view when enabled in milliseconds | `250` |
| fitViewPadding | Extra space around points when fitting view | `0.1` |
| fitViewDuration | Animation duration for fit view operation in milliseconds | `250` |
| fitViewByPointsInRect | When `fitViewOnInit` is set to `true`, fits the view to show the points within a rectangle defined by its two corner coordinates `[[left, bottom], [right, top]]` in the scene space | `undefined` |
| fitViewByPointIndices | When `fitViewOnInit` is set to `true`, fits the view to show only the specified points by their indices. Takes precedence over `fitViewByPointsInRect` when both are provided. | `undefined` |
| randomSeed | Providing a value allows control over layout randomness for consistency across simulations. Applied only on initialization | `undefined` |
| pointSamplingDistance | Sampling density for point position methods (used in `getSampledPointPositionsMap`) in pixels | `150` |
| attribution | Controls the text shown in the bottom right corner. Provide HTML content as a string for custom attribution. Empty string hides attribution | `''` |
| rescalePositions | Control automatic point position adjustment. When undefined: auto-rescale if simulation disabled | `undefined` |

**Notes:**
- The attribution text color can be customized using CSS variable `--cosmosgl-attribution-color`.
- Error message text color can be customized using CSS variable `--cosmosgl-error-message-color`.

## Simulation configuration

cosmos.gl layout algorithm was inspired by the [d3-force](https://github.com/d3/d3-force#forces) simulation forces: Link, Many-Body, Gravitation, and Centering. It provides several simulation settings to adjust the layout. Each of them can be changed in real time, while the simulation is in progress.

| Property | Description  | Recommended range | Default |
|---|---|---|---|
| simulationDecay | Force simulation decay coefficient. <br /><br />Use smaller values if you want the simulation to "cool down" slower.| 100 – 10 000| `5000` |
| simulationGravity | Gravity force coefficient | 0.0 – 1.0 | `0.25` |
| simulationCenter | Centering force coefficient | 0.0 – 1.0 | `0.0` |
| simulationRepulsion | Repulsion force coefficient | 0.0 – 2.0 | `1.0` |
| simulationRepulsionTheta | Decreases / increases the detalization of the Many-Body force calculations. <br /><br /> When `useClassicQuadtree` is set to `true`, this property corresponds to the Barnes–Hut approximation criterion. | 0.3 – 2.0 | `1.15` |
| simulationRepulsionQuadtreeLevels | Barnes–Hut approximation depth. <br /><br />Can only be used when `useClassicQuadtree` is set `true`.  | 5 – 12 | `12` | 
| simulationLinkSpring | Link spring force coefficient | 0.0 – 2.0 | `1.0` |
| simulationLinkDistance | Minimum link distance | 1 – 20 | `10` |
| simulationLinkDistRandomVariationRange | Link distance randomness multiplier range | [0.8 – 1.2,<br/> 1.2 – 2.0] | `[1.0, 1.2]` |
| simulationRepulsionFromMouse | Repulsion from the mouse pointer force coefficient. The repulsion force is activated by pressing the right mouse button. | 0.0 – 5.0 | `2.0`
| enableRightClickRepulsion | Enable or disable the repulsion force from mouse when right-clicking. When set to `true`, holding the right mouse button will activate the mouse repulsion force. When set to `false`, right-clicking will not trigger any repulsion force. | - | `false` |
| simulationFriction | Friction coefficient. Values range from `0` (high friction, stops quickly) to `1` (no friction, keeps moving). | 0.0 – 1.0 | `0.85` |
| simulationCluster | Cluster coefficient | 0.0 – 1.0 | `0.1` |

## Event Callbacks

| Callback | Description | 
|---|---|
| onSimulationStart | Called when simulation starts |
| onSimulationTick | Called on every simulation tick, with the current alpha value and hover information |
| onSimulationEnd | Called when simulation stops |
| onSimulationPause | Called when simulation pauses |
| onSimulationRestart | Called when simulation restarts |
| onClick | Called on canvas click with point index and position |
| onMouseMove | Called on mouse movement with hover info |
| onPointMouseOver | Called when pointer enters a point |
| onPointMouseOut | Called when pointer leaves a point |
| onZoomStart | Called when zoom/pan starts |
| onZoom | Called during zoom/pan |
| onZoomEnd | Called when zoom/pan ends |
| onDragStart | Called when dragging starts |
| onDrag | Called during dragging |
| onDragEnd | Called when dragging ends |

---
Copyright [OpenJS Foundation](https://openjsf.org) and cosmos.gl contributors. All rights reserved. The [OpenJS Foundation](https://openjsf.org) has registered trademarks and uses trademarks.  For a list of trademarks of the [OpenJS Foundation](https://openjsf.org), please see our [Trademark Policy](https://trademark-policy.openjsf.org/) and [Trademark List](https://trademark-list.openjsf.org/).  Trademarks and logos not indicated on the [list of OpenJS Foundation trademarks](https://trademark-list.openjsf.org) are trademarks™ or registered® trademarks of their respective holders. Use of them does not imply any affiliation with or endorsement by them.
[The OpenJS Foundation](https://openjsf.org/) | [Terms of Use](https://terms-of-use.openjsf.org/) | [Privacy Policy](https://privacy-policy.openjsf.org/) | [Bylaws](https://bylaws.openjsf.org/) | [Code of Conduct](https://code-of-conduct.openjsf.org) | [Trademark Policy](https://trademark-policy.openjsf.org/) | [Trademark List](https://trademark-list.openjsf.org/) | [Cookie Policy](https://www.linuxfoundation.org/cookies/)