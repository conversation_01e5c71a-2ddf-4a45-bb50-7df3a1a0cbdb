{"version": 3, "file": "events.js", "sources": ["../../../../../../node_modules/@material/dom/events.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n/**\n * Determine whether the current browser supports passive event listeners, and\n * if so, use them.\n */\nexport function applyPassive(globalObj) {\n    if (globalObj === void 0) { globalObj = window; }\n    return supportsPassiveOption(globalObj) ?\n        { passive: true } :\n        false;\n}\nfunction supportsPassiveOption(globalObj) {\n    if (globalObj === void 0) { globalObj = window; }\n    // See\n    // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener\n    var passiveSupported = false;\n    try {\n        var options = {\n            // This function will be called when the browser\n            // attempts to access the passive property.\n            get passive() {\n                passiveSupported = true;\n                return false;\n            }\n        };\n        var handler = function () { };\n        globalObj.document.addEventListener('test', handler, options);\n        globalObj.document.removeEventListener('test', handler, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n    return passiveSupported;\n}\n//# sourceMappingURL=events.js.map"], "names": ["applyPassive", "globalObj", "window", "passiveSupported", "options", "passive", "handler", "document", "addEventListener", "removeEventListener", "err", "supportsPassiveOption"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AA0BO,SAASA,EAAaC,GAEzB,YADkB,IAAdA,IAAwBA,EAAYC,UAK5C,SAA+BD,QACT,IAAdA,IAAwBA,EAAYC,QAGxC,IAAIC,GAAmB,EACvB,IACI,IAAIC,EAAU,CAGV,WAAIC,GAEA,OADAF,GAAmB,GACZ,CACV,GAEDG,EAAU,aACdL,EAAUM,SAASC,iBAAiB,OAAQF,EAASF,GACrDH,EAAUM,SAASE,oBAAoB,OAAQH,EAASF,EAC3D,CACD,MAAOM,GACHP,GAAmB,CACtB,CACD,OAAOA,CACX,CA1BWQ,CAAsBV,IACzB,CAAEI,SAAS,EAEnB"}