{"version": 3, "file": "index.js", "sources": ["../../../src/modules/histogram/index.ts"], "sourcesContent": ["import { axisBottom } from 'd3-axis'\nimport { ResizeObserver } from '@juggle/resize-observer'\nimport { type BrushBehavior, brushX } from 'd3-brush'\nimport { scaleLinear, scaleSymlog } from 'd3-scale'\nimport { type Selection, select } from 'd3-selection'\nimport { extent, rollup, pairs } from 'd3-array'\nimport { HistogramConfig, type HistogramConfigInterface, type HistogramEvents } from './config'\nimport { getCountsInRange } from '../../utils'\nimport type { BarData } from './types'\n\nimport s from './style.module.css'\n\nexport class Histogram {\n  private _config = new HistogramConfig()\n\n  private _svg: SVGElement\n  private _containerNode: HTMLElement\n  private _noDataDiv: HTMLElement\n  private _resizeObserver: ResizeObserver\n  private _axisGroup: Selection<SVGGElement, unknown, null, undefined>\n  private _barsGroup: Selection<SVGGElement, unknown, null, undefined>\n  private _highlightedBarsGroup: Selection<SVGGElement, unknown, null, undefined>\n  private _brushGroup: Selection<SVGGElement, unknown, null, undefined>\n  private _firstRender = true\n\n  private _formatter: ((n: number) => string) | undefined\n  private _height: number\n  private _width: number\n  private _histogramWidth: number\n  private _histogramHeight: number\n\n  private _barWidth = 0\n  private _maxCount = 0\n\n  private _extent: [number, number] | undefined\n  private _barsData: BarData[] = []\n  private _highlightedBarsData: BarData[] = []\n  private _histogramData?: number[] = undefined\n  private _highlightedData?: number[] = undefined\n  private _bandIntervals: number[] = []\n  private _calculatedStep = 0\n  private _currentSelection: [number, number] | undefined\n  private _currentSelectionInPixels: [number, number] | undefined\n\n  private _yScale = scaleSymlog<number, number, never>()\n  private _xScale = scaleLinear<number, number, never>()\n\n  private _axis = axisBottom<number>(this._xScale)\n  private _brushInstance!: BrushBehavior<unknown> | undefined\n\n  public constructor (containerNode: HTMLElement, config?: HistogramConfigInterface) {\n    const { offsetWidth, offsetHeight } = containerNode\n    if (config) this._config.init(config)\n    this._containerNode = containerNode\n    this._containerNode.classList.add(s.histogram)\n    this._width = offsetWidth\n    this._height = offsetHeight\n    this._formatter = config?.formatter ?? undefined\n    this._histogramWidth = this._width - this._config.padding.left - this._config.padding.right\n    this._histogramHeight = this._height - this._config.padding.top - this._config.padding.bottom\n\n    this._svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')\n    this._svg.classList.add(s.histogramSvg)\n    this._containerNode.appendChild(this._svg)\n\n    this._noDataDiv = document.createElement('div')\n    select(this._noDataDiv).style('display', 'none').attr('class', s.noData).append('div').text('No histogram data')\n    this._containerNode.appendChild(this._noDataDiv)\n\n    this._axisGroup = select(this._svg).append('g').attr('class', s.axis)\n    this._barsGroup = select(this._svg).append('g').attr('class', s.bars)\n    this._highlightedBarsGroup = select(this._svg).append('g').attr('class', s.bars)\n    this._brushGroup = select(this._svg).append('g').attr('class', s.brush)\n    this._axis.tickFormat((d) => (this._formatter ? this._formatter(d) : d.toFixed(0)))\n\n    this._resizeObserver = new ResizeObserver(() => {\n      this.resize()\n    })\n    this._resizeObserver.observe(this._containerNode)\n  }\n\n  private get _barPadding (): number {\n    return this._barWidth * this._config.barPadding\n  }\n\n  /**  `getCurrentSelection`: Returns current brush selection. */\n  public get getCurrentSelection (): number[] | undefined {\n    return this._currentSelection\n  }\n\n  /**  `getCurrentSelectionInPixels`: Returns current brush selection in pixels. */\n  public get getCurrentSelectionInPixels (): number[] {\n    return this._currentSelectionInPixels ?? []\n  }\n\n  /**  `getBarWidth`: Returns computed bar width in pixels */\n  public getBarWidth (): number {\n    return this._barWidth - this._barPadding\n  }\n\n  /**  `getConfig`: Returns current `Histogram` configuration */\n  public getConfig (): HistogramConfig {\n    return this._config\n  }\n\n  /**  `setConfig`: Function for setting config of `Histogram`. */\n  public setConfig (config?: HistogramConfigInterface): void {\n    const prevConfig = JSON.parse(JSON.stringify(this._config))\n    if (!config) {\n      this._config = new HistogramConfig()\n    } else {\n      this._config.init(config)\n    }\n    if (!this._config.allowSelection) {\n      this._disableBrush()\n    }\n    // Data-related props\n    if (this._config?.dataStep !== prevConfig.config?.dataStep || this._config?.barCount !== prevConfig.config?.barCount) {\n      this._updateHistogramData()\n      this._updateHistogramHighlightedData()\n    }\n    this.resize()\n  }\n\n  /**  `setHistogramData`: Function for setting data of `Histogram`. */\n  public setHistogramData (data: number[] | undefined, customExtent?: [number, number]): void {\n    if (customExtent && customExtent[1] <= customExtent[0]) return\n    this._histogramData = data?.filter((d) => !isNaN(d) && d !== undefined)\n    this._highlightedBarsData = []\n    this._currentSelection = undefined\n    this._brushCurrentSelection()\n    this._config.events.onBrush?.(this._currentSelection)\n    select(this._noDataDiv).style('display', 'none')\n    if (this._histogramData?.length) {\n      this._extent = customExtent || (extent(this._histogramData) as [number, number])\n      this._firstRender = true\n      this._updateHistogramData()\n      this._updateScales()\n      this.render()\n    } else {\n      select(this._noDataDiv).style('display', 'block')\n    }\n  }\n\n  /**  `setHighlightedData`: Function for setting highlighted data of `Histogram`. */\n  public setHighlightedData (data: number[] | undefined): void {\n    if (!this._histogramData) return\n    if (!data || data.length === 0) {\n      const prevData = JSON.stringify(this._highlightedBarsData)\n      this._highlightedData = []\n      this._highlightedBarsData = []\n      if (prevData !== JSON.stringify(this._highlightedBarsData)) {\n        this._updateBars(true)\n      }\n    } else {\n      this._highlightedData = data?.filter((d) => !isNaN(d) && d !== undefined)\n      this._updateHistogramHighlightedData()\n      this._updateBars(true)\n    }\n  }\n\n  /**  `setSelection`: Set the selected range on a `Histogram`. Takes a numeric selection range in the X axis u nits as a parameter. */\n  public setSelection (selection?: [number, number], renderOnly = false): void {\n    const prevSelection = this._currentSelection\n    if (selection && this._extent && selection[0] >= this._extent[0] && selection[1] <= this._extent[1] && selection[0] < selection[1]) {\n      const selectionInPixels = selection.map(this._xScale) as [number, number]\n      this._mapSelection(selectionInPixels)\n    } else {\n      this._currentSelection = undefined\n    }\n    const { _currentSelection } = this\n    if (!renderOnly && (prevSelection?.[0] !== _currentSelection?.[0] || prevSelection?.[1] !== _currentSelection?.[1])) {\n      this._config.events.onBrush?.(this._currentSelection, true)\n    }\n    this._brushCurrentSelection()\n  }\n\n  /**  `resize`: Resizes `Histogram` according to the parent node attributes. */\n  public resize (): void {\n    const { offsetWidth, offsetHeight } = this._containerNode\n    this._width = offsetWidth\n    this._height = offsetHeight\n    this._histogramWidth = this._width - this._config.padding.left - this._config.padding.right\n    this._histogramHeight = this._height - this._config.padding.top - this._config.padding.bottom\n    if (this._histogramHeight > this._config.padding.top + this._config.padding.bottom) {\n      this._updateScales()\n      if (this._currentSelection) this.setSelection(this._currentSelection, true)\n      if (!this._firstRender) this.render()\n    }\n  }\n\n  /**  `render`: Renders `Histogram`. */\n  public render (): void {\n    this._updateBrush()\n    this._updateBars()\n    this._updateBars(true)\n    this._updateAxis()\n    if (this._firstRender) this._firstRender = false\n  }\n\n  public destroy (): void {\n    this._containerNode.innerHTML = ''\n  }\n\n  private _updateAxis (): void {\n    if (!this._histogramData) return\n    this._axisGroup\n      .style('transform', `translate(${this._config.padding.left}px, ${this._config.padding.top - this._config.selectionPadding / 2 + 1}px)`)\n      .call(this._axis)\n      .call((g) => g.select('.domain').remove())\n\n    this._axisGroup.selectAll('.tick').select('text').attr('class', s.axisTick).attr('y', 0).attr('dy', 0).attr('dx', this._config.labelSideMargin)\n\n    this._axisGroup.selectAll('.tick:last-of-type text').attr('dx', -this._config.labelSideMargin).style('text-anchor', 'end')\n\n    this._axisGroup.selectAll('line').attr('y2', this._histogramHeight).attr('y1', 0).attr('opacity', 0.1)\n  }\n\n  private _updateBrush (): void {\n    if (!this._config.allowSelection) return\n    this._brushGroup.style('transform', `translate(${this._config.padding.left}px, ${this._config.padding.top}px)`)\n    this._brushInstance = brushX().extent([\n      [0, 0],\n      [this._histogramWidth, this._histogramHeight],\n    ])\n    this._brushInstance.on('end', ({ selection, sourceEvent }: { selection: [number, number]; sourceEvent: MouseEvent }) => {\n      if (!sourceEvent) return\n      if (selection) {\n        this._mapSelection(selection)\n        this._config.events.onBrush?.(this._currentSelection)\n      } else {\n        this._currentSelection = undefined\n        this._config.events.onBrush?.(undefined)\n      }\n      this._brushCurrentSelection()\n    })\n    this._brushGroup.call(this._brushInstance)\n    this._brushGroup.select('rect.selection').classed(s.selection, true).attr('rx', this._config.selectionRadius).attr('ry', this._config.selectionRadius)\n  }\n\n  private _updateBars (highlighted?: boolean): void {\n    const target = highlighted ? this._highlightedBarsGroup : this._barsGroup\n    target.style('transform', `translate(${this._config.padding.left}px, ${this._config.padding.top - this._config.selectionPadding / 2}px)`)\n    const opacity = highlighted ? 0.5 : 1\n    const bars = target\n      .selectAll(`.${highlighted ? s.highlightedBar : s.bar}`)\n      .data(highlighted ? this._highlightedBarsData : this._barsData)\n      .join('rect')\n      .attr('class', highlighted ? s.highlightedBar : s.bar)\n      .attr('x', (d) => this._xScale(d.rangeStart) + this._barPadding / 2)\n      .attr('width', this.getBarWidth())\n      .attr('rx', this._config.barRadius)\n      .attr('ry', this._config.barRadius)\n      .attr('y', -this._histogramHeight)\n\n    if (!highlighted) {\n      if (this._config.events.onBarHover) bars.on('mouseover', this._config.events.onBarHover)\n    }\n\n    bars\n      .transition()\n      .duration(300)\n      .attr('height', (d) => {\n        // Do not apply `minBarHeight` for highlighted layer\n        if (highlighted) return d.count === 0 ? 0 : this._yScale(d.count)\n        else return this._yScale(d.count)\n      })\n      .style('opacity', (d) => (this._yScale(d.count) === this._config.minBarHeight ? 0.4 : opacity))\n  }\n\n  private _updateScales (): void {\n    if (!this._extent || !this._barsData.length) return\n    const lastBar = this._barsData[this._barsData.length - 1] as BarData\n    const precisedExtent = this._config.dataStep ? [this._extent[0], lastBar.rangeEnd] : this._extent\n    this._xScale.domain(precisedExtent).range([0, this._histogramWidth]).clamp(true)\n    this._yScale\n      .range([this._config.minBarHeight, this._histogramHeight - this._config.barTopMargin - this._config.selectionPadding])\n      .domain([0, this._maxCount])\n      .clamp(true)\n    this._axis.tickValues(precisedExtent)\n    this._barWidth = this._xScale(lastBar.rangeEnd) - this._xScale(lastBar.rangeStart)\n  }\n\n  private _disableBrush (): void {\n    this._currentSelection = undefined\n    this._brushCurrentSelection()\n    this._brushGroup.selectAll('*').remove()\n  }\n\n  private _updateHistogramData (): void {\n    if (this._histogramData?.length && this._extent) {\n      const countedNumbers = rollup(\n        this._histogramData,\n        (v) => v.length,\n        (d) => d\n      )\n      const step = this._config.dataStep ?? Math.abs(this._extent[1] - this._extent[0]) / (this._config.barCount - 1)\n      if (step === 0) return\n      else this._calculatedStep = step\n      this._bandIntervals = this._generateSequence(this._extent[0], this._extent[1], this._calculatedStep)\n      if (this._config.dataStep) {\n        const lastTick = this._bandIntervals[this._bandIntervals.length - 1] as number\n        if (lastTick < this._extent[1]) {\n          this._bandIntervals.push(+lastTick + this._calculatedStep)\n        }\n      }\n      const valuePairs = pairs(this._bandIntervals)\n      this._barsData = valuePairs.map((d) => ({\n        rangeStart: d[0],\n        rangeEnd: d[1],\n        count: getCountsInRange(countedNumbers, d),\n      }))\n      this._maxCount = Math.max(...this._barsData.map((d) => d.count))\n    }\n  }\n\n  private _updateHistogramHighlightedData (): void {\n    if (this._highlightedData?.length && this._extent) {\n      const countedNumbers = rollup(\n        this._highlightedData,\n        (v) => v.length,\n        (d) => d\n      )\n      this._bandIntervals = this._generateSequence(this._extent[0], this._extent[1], this._calculatedStep)\n      const valuePairs = pairs(this._bandIntervals)\n      this._highlightedBarsData = valuePairs.map((d, i) => {\n        let count = getCountsInRange(countedNumbers, d)\n        const targetBar = this._barsData[i] as BarData\n        if (targetBar) {\n          if (count > (this._barsData[i] as BarData).count) count = (this._barsData[i] as BarData).count\n        }\n        return {\n          rangeStart: targetBar.rangeStart,\n          rangeEnd: targetBar.rangeEnd,\n          count,\n        }\n      })\n    }\n  }\n\n  private _mapSelection (selectionInPx: [number, number]): void {\n    if (!this._barsData.length) return\n    if (this._config.stickySelection) {\n      this._currentSelection = [this._getClosestRange(this._xScale.invert(selectionInPx[0])).rangeStart, this._getClosestRange(this._xScale.invert(selectionInPx[1]), true).rangeEnd]\n      if (this._currentSelection[0] === this._currentSelection[1]) this._currentSelection = undefined\n    } else {\n      this._currentSelection = selectionInPx.map(this._xScale.invert) as [number, number]\n    }\n  }\n\n  private _brushCurrentSelection (): void {\n    if (this._currentSelection) {\n      this._currentSelectionInPixels = this._currentSelection.map(this._xScale) as [number, number]\n      if (this._brushInstance && !this._firstRender) this._brushGroup.call(this._brushInstance.move, this._currentSelectionInPixels)\n    } else {\n      this._currentSelectionInPixels = undefined\n      this._brushInstance?.clear(this._brushGroup)\n    }\n  }\n\n  private _generateSequence (start: number, end: number, step: number): number[] {\n    const count = Math.round((end - start) / step) + 1\n    const arr = new Array(count)\n    for (let i = 0; i < count; i++) {\n      arr[i] = start + i * step\n    }\n    return arr\n  }\n\n  private _getClosestRange = (goal: number, end?: boolean): BarData =>\n    this._barsData?.reduce((prev, curr) => (Math.abs(curr[end ? 'rangeEnd' : 'rangeStart'] - goal) < Math.abs(prev[end ? 'rangeEnd' : 'rangeStart'] - goal) ? curr : prev))\n}\n\nexport { HistogramConfig }\nexport type { HistogramConfigInterface, HistogramEvents }\n"], "names": ["Histogram", "constructor", "containerNode", "config", "this", "_config", "HistogramConfig", "_firstRender", "_barWidth", "_maxCount", "_barsData", "_highlightedBarsData", "_histogramData", "undefined", "_highlightedData", "_bandIntervals", "_calculatedStep", "_yScale", "scaleSymlog", "_xScale", "scaleLinear", "_axis", "axisBottom", "_getClosestRange", "goal", "end", "_a", "reduce", "prev", "curr", "Math", "abs", "offsetWidth", "offsetHeight", "init", "_containerNode", "classList", "add", "s", "histogram", "_width", "_height", "_formatter", "formatter", "_histogramWidth", "padding", "left", "right", "_histogramHeight", "top", "bottom", "_svg", "document", "createElementNS", "histogramSvg", "append<PERSON><PERSON><PERSON>", "_noDataDiv", "createElement", "select", "style", "attr", "noData", "append", "text", "_axisGroup", "axis", "_barsGroup", "bars", "_highlightedBarsGroup", "_brushGroup", "brush", "tickFormat", "d", "toFixed", "_resizeObserver", "ResizeObserver", "resize", "observe", "_barPadding", "barPadding", "getCurrentSelection", "_currentSelection", "getCurrentSelectionInPixels", "_currentSelectionInPixels", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getConfig", "setConfig", "prevConfig", "JSON", "parse", "stringify", "allowSelection", "_disableBrush", "dataStep", "_b", "_c", "barCount", "_d", "_updateHistogramData", "_updateHistogramHighlightedData", "setHistogramData", "data", "customExtent", "filter", "isNaN", "_brushCurrentSelection", "events", "onBrush", "call", "length", "_extent", "extent", "_updateScales", "render", "setHighlightedData", "_updateBars", "prevData", "setSelection", "selection", "renderOnly", "prevSelection", "selectionInPixels", "map", "_mapSelection", "_updateBrush", "_updateAxis", "destroy", "innerHTML", "selectionPadding", "g", "remove", "selectAll", "axisTick", "labelSideMargin", "_brushInstance", "brushX", "on", "sourceEvent", "classed", "selectionRadius", "highlighted", "target", "opacity", "<PERSON><PERSON><PERSON>", "bar", "join", "rangeStart", "barRadius", "onBarHover", "transition", "duration", "count", "minBarHeight", "lastBar", "precisedExtent", "rangeEnd", "domain", "range", "clamp", "bar<PERSON>op<PERSON>argin", "tickValues", "countedNumbers", "rollup", "v", "step", "_generateSequence", "lastTick", "push", "valuePairs", "pairs", "getCountsInRange", "max", "i", "targetBar", "selectionInPx", "stickySelection", "invert", "move", "clear", "start", "round", "arr", "Array"], "mappings": "2cAYaA,EAsCX,WAAAC,CAAoBC,EAA4BC,SArCxCC,KAAAC,QAAU,IAAIC,EAUdF,KAAYG,cAAG,EAQfH,KAASI,UAAG,EACZJ,KAASK,UAAG,EAGZL,KAASM,UAAc,GACvBN,KAAoBO,qBAAc,GAClCP,KAAcQ,oBAAcC,EAC5BT,KAAgBU,sBAAcD,EAC9BT,KAAcW,eAAa,GAC3BX,KAAeY,gBAAG,EAIlBZ,KAAOa,QAAGC,IACVd,KAAOe,QAAGC,IAEVhB,KAAAiB,MAAQC,EAAmBlB,KAAKe,SAkUhCf,KAAAmB,iBAAmB,CAACC,EAAcC,KACxC,IAAAC,EAAA,OAAc,UAAdtB,KAAKM,iBAAS,IAAAgB,OAAA,EAAAA,EAAEC,QAAO,CAACC,EAAMC,IAAUC,KAAKC,IAAIF,EAAKJ,EAAM,WAAa,cAAgBD,GAAQM,KAAKC,IAAIH,EAAKH,EAAM,WAAa,cAAgBD,GAAQK,EAAOD,GAAM,EA/TvK,MAAMI,YAAEA,EAAWC,aAAEA,GAAiB/B,EAClCC,GAAQC,KAAKC,QAAQ6B,KAAK/B,GAC9BC,KAAK+B,eAAiBjC,EACtBE,KAAK+B,eAAeC,UAAUC,IAAIC,EAAEC,WACpCnC,KAAKoC,OAASR,EACd5B,KAAKqC,QAAUR,EACf7B,KAAKsC,WAA8B,QAAjBhB,EAAAvB,aAAM,EAANA,EAAQwC,iBAAS,IAAAjB,EAAAA,OAAIb,EACvCT,KAAKwC,gBAAkBxC,KAAKoC,OAASpC,KAAKC,QAAQwC,QAAQC,KAAO1C,KAAKC,QAAQwC,QAAQE,MACtF3C,KAAK4C,iBAAmB5C,KAAKqC,QAAUrC,KAAKC,QAAQwC,QAAQI,IAAM7C,KAAKC,QAAQwC,QAAQK,OAEvF9C,KAAK+C,KAAOC,SAASC,gBAAgB,6BAA8B,OACnEjD,KAAK+C,KAAKf,UAAUC,IAAIC,EAAEgB,cAC1BlD,KAAK+B,eAAeoB,YAAYnD,KAAK+C,MAErC/C,KAAKoD,WAAaJ,SAASK,cAAc,OACzCC,EAAOtD,KAAKoD,YAAYG,MAAM,UAAW,QAAQC,KAAK,QAAStB,EAAEuB,QAAQC,OAAO,OAAOC,KAAK,qBAC5F3D,KAAK+B,eAAeoB,YAAYnD,KAAKoD,YAErCpD,KAAK4D,WAAaN,EAAOtD,KAAK+C,MAAMW,OAAO,KAAKF,KAAK,QAAStB,EAAE2B,MAChE7D,KAAK8D,WAAaR,EAAOtD,KAAK+C,MAAMW,OAAO,KAAKF,KAAK,QAAStB,EAAE6B,MAChE/D,KAAKgE,sBAAwBV,EAAOtD,KAAK+C,MAAMW,OAAO,KAAKF,KAAK,QAAStB,EAAE6B,MAC3E/D,KAAKiE,YAAcX,EAAOtD,KAAK+C,MAAMW,OAAO,KAAKF,KAAK,QAAStB,EAAEgC,OACjElE,KAAKiB,MAAMkD,YAAYC,GAAOpE,KAAKsC,WAAatC,KAAKsC,WAAW8B,GAAKA,EAAEC,QAAQ,KAE/ErE,KAAKsE,gBAAkB,IAAIC,GAAe,KACxCvE,KAAKwE,QAAQ,IAEfxE,KAAKsE,gBAAgBG,QAAQzE,KAAK+B,eACnC,CAED,eAAY2C,GACV,OAAO1E,KAAKI,UAAYJ,KAAKC,QAAQ0E,UACtC,CAGD,uBAAWC,GACT,OAAO5E,KAAK6E,iBACb,CAGD,+BAAWC,SACT,OAAqC,UAA9B9E,KAAK+E,iCAAyB,IAAAzD,EAAAA,EAAI,EAC1C,CAGM,WAAA0D,GACL,OAAOhF,KAAKI,UAAYJ,KAAK0E,WAC9B,CAGM,SAAAO,GACL,OAAOjF,KAAKC,OACb,CAGM,SAAAiF,CAAWnF,eAChB,MAAMoF,EAAaC,KAAKC,MAAMD,KAAKE,UAAUtF,KAAKC,UAC7CF,EAGHC,KAAKC,QAAQ6B,KAAK/B,GAFlBC,KAAKC,QAAU,IAAIC,EAIhBF,KAAKC,QAAQsF,gBAChBvF,KAAKwF,iBAGS,QAAZlE,EAAAtB,KAAKC,eAAO,IAAAqB,OAAA,EAAAA,EAAEmE,qBAAaC,EAAAP,EAAWpF,6BAAQ0F,oBAAYE,EAAA3F,KAAKC,8BAAS2F,aAAgC,QAAnBC,EAAAV,EAAWpF,cAAQ,IAAA8F,OAAA,EAAAA,EAAAD,YAC1G5F,KAAK8F,uBACL9F,KAAK+F,mCAEP/F,KAAKwE,QACN,CAGM,gBAAAwB,CAAkBC,EAA4BC,aAC/CA,GAAgBA,EAAa,IAAMA,EAAa,KACpDlG,KAAKQ,eAAiByF,aAAI,EAAJA,EAAME,QAAQ/B,IAAOgC,MAAMhC,SAAY3D,IAAN2D,IACvDpE,KAAKO,qBAAuB,GAC5BP,KAAK6E,uBAAoBpE,EACzBT,KAAKqG,yBACyB,QAA9BX,GAAApE,EAAAtB,KAAKC,QAAQqG,QAAOC,eAAU,IAAAb,GAAAA,EAAAc,KAAAlF,EAAAtB,KAAK6E,mBACnCvB,EAAOtD,KAAKoD,YAAYG,MAAM,UAAW,SAClB,UAAnBvD,KAAKQ,sBAAc,IAAAmF,OAAA,EAAAA,EAAEc,SACvBzG,KAAK0G,QAAUR,GAAiBS,EAAO3G,KAAKQ,gBAC5CR,KAAKG,cAAe,EACpBH,KAAK8F,uBACL9F,KAAK4G,gBACL5G,KAAK6G,UAELvD,EAAOtD,KAAKoD,YAAYG,MAAM,UAAW,SAE5C,CAGM,kBAAAuD,CAAoBb,GACzB,GAAKjG,KAAKQ,eACV,GAAKyF,GAAwB,IAAhBA,EAAKQ,OAQhBzG,KAAKU,iBAAmBuF,aAAI,EAAJA,EAAME,QAAQ/B,IAAOgC,MAAMhC,SAAY3D,IAAN2D,IACzDpE,KAAK+F,kCACL/F,KAAK+G,aAAY,OAVa,CAC9B,MAAMC,EAAW5B,KAAKE,UAAUtF,KAAKO,sBACrCP,KAAKU,iBAAmB,GACxBV,KAAKO,qBAAuB,GACxByG,IAAa5B,KAAKE,UAAUtF,KAAKO,uBACnCP,KAAK+G,aAAY,EAEpB,CAKF,CAGM,YAAAE,CAAcC,EAA8BC,GAAa,WAC9D,MAAMC,EAAgBpH,KAAK6E,kBAC3B,GAAIqC,GAAalH,KAAK0G,SAAWQ,EAAU,IAAMlH,KAAK0G,QAAQ,IAAMQ,EAAU,IAAMlH,KAAK0G,QAAQ,IAAMQ,EAAU,GAAKA,EAAU,GAAI,CAClI,MAAMG,EAAoBH,EAAUI,IAAItH,KAAKe,SAC7Cf,KAAKuH,cAAcF,EACpB,MACCrH,KAAK6E,uBAAoBpE,EAE3B,MAAMoE,kBAAEA,GAAsB7E,KACzBmH,IAAeC,aAAA,EAAAA,EAAgB,OAAOvC,aAAA,EAAAA,EAAoB,MAAMuC,eAAAA,EAAgB,OAAOvC,eAAAA,EAAoB,KACnF,QAA3Ba,GAAApE,EAAAtB,KAAKC,QAAQqG,QAAOC,eAAO,IAAAb,GAAAA,EAAAc,KAAAlF,EAAGtB,KAAK6E,mBAAmB,GAExD7E,KAAKqG,wBACN,CAGM,MAAA7B,GACL,MAAM5C,YAAEA,EAAWC,aAAEA,GAAiB7B,KAAK+B,eAC3C/B,KAAKoC,OAASR,EACd5B,KAAKqC,QAAUR,EACf7B,KAAKwC,gBAAkBxC,KAAKoC,OAASpC,KAAKC,QAAQwC,QAAQC,KAAO1C,KAAKC,QAAQwC,QAAQE,MACtF3C,KAAK4C,iBAAmB5C,KAAKqC,QAAUrC,KAAKC,QAAQwC,QAAQI,IAAM7C,KAAKC,QAAQwC,QAAQK,OACnF9C,KAAK4C,iBAAmB5C,KAAKC,QAAQwC,QAAQI,IAAM7C,KAAKC,QAAQwC,QAAQK,SAC1E9C,KAAK4G,gBACD5G,KAAK6E,mBAAmB7E,KAAKiH,aAAajH,KAAK6E,mBAAmB,GACjE7E,KAAKG,cAAcH,KAAK6G,SAEhC,CAGM,MAAAA,GACL7G,KAAKwH,eACLxH,KAAK+G,cACL/G,KAAK+G,aAAY,GACjB/G,KAAKyH,cACDzH,KAAKG,eAAcH,KAAKG,cAAe,EAC5C,CAEM,OAAAuH,GACL1H,KAAK+B,eAAe4F,UAAY,EACjC,CAEO,WAAAF,GACDzH,KAAKQ,iBACVR,KAAK4D,WACFL,MAAM,YAAa,aAAavD,KAAKC,QAAQwC,QAAQC,WAAW1C,KAAKC,QAAQwC,QAAQI,IAAM7C,KAAKC,QAAQ2H,iBAAmB,EAAI,QAC/HpB,KAAKxG,KAAKiB,OACVuF,MAAMqB,GAAMA,EAAEvE,OAAO,WAAWwE,WAEnC9H,KAAK4D,WAAWmE,UAAU,SAASzE,OAAO,QAAQE,KAAK,QAAStB,EAAE8F,UAAUxE,KAAK,IAAK,GAAGA,KAAK,KAAM,GAAGA,KAAK,KAAMxD,KAAKC,QAAQgI,iBAE/HjI,KAAK4D,WAAWmE,UAAU,2BAA2BvE,KAAK,MAAOxD,KAAKC,QAAQgI,iBAAiB1E,MAAM,cAAe,OAEpHvD,KAAK4D,WAAWmE,UAAU,QAAQvE,KAAK,KAAMxD,KAAK4C,kBAAkBY,KAAK,KAAM,GAAGA,KAAK,UAAW,IACnG,CAEO,YAAAgE,GACDxH,KAAKC,QAAQsF,iBAClBvF,KAAKiE,YAAYV,MAAM,YAAa,aAAavD,KAAKC,QAAQwC,QAAQC,WAAW1C,KAAKC,QAAQwC,QAAQI,UACtG7C,KAAKkI,eAAiBC,IAASxB,OAAO,CACpC,CAAC,EAAG,GACJ,CAAC3G,KAAKwC,gBAAiBxC,KAAK4C,oBAE9B5C,KAAKkI,eAAeE,GAAG,OAAO,EAAGlB,YAAWmB,8BACrCA,IACDnB,GACFlH,KAAKuH,cAAcL,GACW,QAA9BxB,GAAApE,EAAAtB,KAAKC,QAAQqG,QAAOC,eAAU,IAAAb,GAAAA,EAAAc,KAAAlF,EAAAtB,KAAK6E,qBAEnC7E,KAAK6E,uBAAoBpE,EACE,QAA3BoF,GAAAF,EAAA3F,KAAKC,QAAQqG,QAAOC,eAAO,IAAAV,GAAAA,EAAAW,KAAAb,OAAGlF,IAEhCT,KAAKqG,yBAAwB,IAE/BrG,KAAKiE,YAAYuC,KAAKxG,KAAKkI,gBAC3BlI,KAAKiE,YAAYX,OAAO,kBAAkBgF,QAAQpG,EAAEgF,WAAW,GAAM1D,KAAK,KAAMxD,KAAKC,QAAQsI,iBAAiB/E,KAAK,KAAMxD,KAAKC,QAAQsI,iBACvI,CAEO,WAAAxB,CAAayB,GACnB,MAAMC,EAASD,EAAcxI,KAAKgE,sBAAwBhE,KAAK8D,WAC/D2E,EAAOlF,MAAM,YAAa,aAAavD,KAAKC,QAAQwC,QAAQC,WAAW1C,KAAKC,QAAQwC,QAAQI,IAAM7C,KAAKC,QAAQ2H,iBAAmB,QAClI,MAAMc,EAAUF,EAAc,GAAM,EAC9BzE,EAAO0E,EACVV,UAAU,IAAIS,EAActG,EAAEyG,eAAiBzG,EAAE0G,OACjD3C,KAAKuC,EAAcxI,KAAKO,qBAAuBP,KAAKM,WACpDuI,KAAK,QACLrF,KAAK,QAASgF,EAActG,EAAEyG,eAAiBzG,EAAE0G,KACjDpF,KAAK,KAAMY,GAAMpE,KAAKe,QAAQqD,EAAE0E,YAAc9I,KAAK0E,YAAc,IACjElB,KAAK,QAASxD,KAAKgF,eACnBxB,KAAK,KAAMxD,KAAKC,QAAQ8I,WACxBvF,KAAK,KAAMxD,KAAKC,QAAQ8I,WACxBvF,KAAK,KAAMxD,KAAK4C,kBAEd4F,GACCxI,KAAKC,QAAQqG,OAAO0C,YAAYjF,EAAKqE,GAAG,YAAapI,KAAKC,QAAQqG,OAAO0C,YAG/EjF,EACGkF,aACAC,SAAS,KACT1F,KAAK,UAAWY,GAEXoE,GAAgC,IAAZpE,EAAE+E,MAAc,EAC5BnJ,KAAKa,QAAQuD,EAAE+E,SAE5B5F,MAAM,WAAYa,GAAOpE,KAAKa,QAAQuD,EAAE+E,SAAWnJ,KAAKC,QAAQmJ,aAAe,GAAMV,GACzF,CAEO,aAAA9B,GACN,IAAK5G,KAAK0G,UAAY1G,KAAKM,UAAUmG,OAAQ,OAC7C,MAAM4C,EAAUrJ,KAAKM,UAAUN,KAAKM,UAAUmG,OAAS,GACjD6C,EAAiBtJ,KAAKC,QAAQwF,SAAW,CAACzF,KAAK0G,QAAQ,GAAI2C,EAAQE,UAAYvJ,KAAK0G,QAC1F1G,KAAKe,QAAQyI,OAAOF,GAAgBG,MAAM,CAAC,EAAGzJ,KAAKwC,kBAAkBkH,OAAM,GAC3E1J,KAAKa,QACF4I,MAAM,CAACzJ,KAAKC,QAAQmJ,aAAcpJ,KAAK4C,iBAAmB5C,KAAKC,QAAQ0J,aAAe3J,KAAKC,QAAQ2H,mBACnG4B,OAAO,CAAC,EAAGxJ,KAAKK,YAChBqJ,OAAM,GACT1J,KAAKiB,MAAM2I,WAAWN,GACtBtJ,KAAKI,UAAYJ,KAAKe,QAAQsI,EAAQE,UAAYvJ,KAAKe,QAAQsI,EAAQP,WACxE,CAEO,aAAAtD,GACNxF,KAAK6E,uBAAoBpE,EACzBT,KAAKqG,yBACLrG,KAAKiE,YAAY8D,UAAU,KAAKD,QACjC,CAEO,oBAAAhC,WACN,IAAyB,QAArBxE,EAAAtB,KAAKQ,sBAAgB,IAAAc,OAAA,EAAAA,EAAAmF,SAAUzG,KAAK0G,QAAS,CAC/C,MAAMmD,EAAiBC,EACrB9J,KAAKQ,gBACJuJ,GAAMA,EAAEtD,SACRrC,GAAMA,IAEH4F,EAAgC,QAAzBtE,EAAA1F,KAAKC,QAAQwF,gBAAY,IAAAC,EAAAA,EAAAhE,KAAKC,IAAI3B,KAAK0G,QAAQ,GAAK1G,KAAK0G,QAAQ,KAAO1G,KAAKC,QAAQ2F,SAAW,GAC7G,GAAa,IAAToE,EAAY,OAGhB,GAFKhK,KAAKY,gBAAkBoJ,EAC5BhK,KAAKW,eAAiBX,KAAKiK,kBAAkBjK,KAAK0G,QAAQ,GAAI1G,KAAK0G,QAAQ,GAAI1G,KAAKY,iBAChFZ,KAAKC,QAAQwF,SAAU,CACzB,MAAMyE,EAAWlK,KAAKW,eAAeX,KAAKW,eAAe8F,OAAS,GAC9DyD,EAAWlK,KAAK0G,QAAQ,IAC1B1G,KAAKW,eAAewJ,MAAMD,EAAWlK,KAAKY,gBAE7C,CACD,MAAMwJ,EAAaC,EAAMrK,KAAKW,gBAC9BX,KAAKM,UAAY8J,EAAW9C,KAAKlD,IAAO,CACtC0E,WAAY1E,EAAE,GACdmF,SAAUnF,EAAE,GACZ+E,MAAOmB,EAAiBT,EAAgBzF,OAE1CpE,KAAKK,UAAYqB,KAAK6I,OAAOvK,KAAKM,UAAUgH,KAAKlD,GAAMA,EAAE+E,QAC1D,CACF,CAEO,+BAAApD,SACN,IAA2B,QAAvBzE,EAAAtB,KAAKU,wBAAkB,IAAAY,OAAA,EAAAA,EAAAmF,SAAUzG,KAAK0G,QAAS,CACjD,MAAMmD,EAAiBC,EACrB9J,KAAKU,kBACJqJ,GAAMA,EAAEtD,SACRrC,GAAMA,IAETpE,KAAKW,eAAiBX,KAAKiK,kBAAkBjK,KAAK0G,QAAQ,GAAI1G,KAAK0G,QAAQ,GAAI1G,KAAKY,iBACpF,MAAMwJ,EAAaC,EAAMrK,KAAKW,gBAC9BX,KAAKO,qBAAuB6J,EAAW9C,KAAI,CAAClD,EAAGoG,KAC7C,IAAIrB,EAAQmB,EAAiBT,EAAgBzF,GAC7C,MAAMqG,EAAYzK,KAAKM,UAAUkK,GAIjC,OAHIC,GACEtB,EAASnJ,KAAKM,UAAUkK,GAAerB,QAAOA,EAASnJ,KAAKM,UAAUkK,GAAerB,OAEpF,CACLL,WAAY2B,EAAU3B,WACtBS,SAAUkB,EAAUlB,SACpBJ,QACD,GAEJ,CACF,CAEO,aAAA5B,CAAemD,GAChB1K,KAAKM,UAAUmG,SAChBzG,KAAKC,QAAQ0K,iBACf3K,KAAK6E,kBAAoB,CAAC7E,KAAKmB,iBAAiBnB,KAAKe,QAAQ6J,OAAOF,EAAc,KAAK5B,WAAY9I,KAAKmB,iBAAiBnB,KAAKe,QAAQ6J,OAAOF,EAAc,KAAK,GAAMnB,UAClKvJ,KAAK6E,kBAAkB,KAAO7E,KAAK6E,kBAAkB,KAAI7E,KAAK6E,uBAAoBpE,IAEtFT,KAAK6E,kBAAoB6F,EAAcpD,IAAItH,KAAKe,QAAQ6J,QAE3D,CAEO,sBAAAvE,SACFrG,KAAK6E,mBACP7E,KAAK+E,0BAA4B/E,KAAK6E,kBAAkByC,IAAItH,KAAKe,SAC7Df,KAAKkI,iBAAmBlI,KAAKG,cAAcH,KAAKiE,YAAYuC,KAAKxG,KAAKkI,eAAe2C,KAAM7K,KAAK+E,6BAEpG/E,KAAK+E,+BAA4BtE,EACZ,QAArBa,EAAAtB,KAAKkI,sBAAgB,IAAA5G,GAAAA,EAAAwJ,MAAM9K,KAAKiE,aAEnC,CAEO,iBAAAgG,CAAmBc,EAAe1J,EAAa2I,GACrD,MAAMb,EAAQzH,KAAKsJ,OAAO3J,EAAM0J,GAASf,GAAQ,EAC3CiB,EAAM,IAAIC,MAAM/B,GACtB,IAAK,IAAIqB,EAAI,EAAGA,EAAIrB,EAAOqB,IACzBS,EAAIT,GAAKO,EAAQP,EAAIR,EAEvB,OAAOiB,CACR"}