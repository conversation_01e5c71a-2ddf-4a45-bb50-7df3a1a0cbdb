{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["mcp__huly-mcp__huly_create_issue", "mcp__huly-mcp__huly_get_issue_details", "mcp__huly-mcp__huly_update_issue", "Bash(git checkout:*)", "<PERSON><PERSON>(curl:*)", "Bash(docker-compose logs:*)", "<PERSON><PERSON>(docker-compose restart:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker stop:*)", "<PERSON><PERSON>(docker rm:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(docker restart:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(git add:*)", "<PERSON><PERSON>(jq:*)", "<PERSON><PERSON>(touch:*)", "Bash(rm:*)", "Bash(cargo build:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "mcp__playwright-http__browser_navigate", "mcp__playwright-http__browser_wait_for", "mcp__playwright-http__browser_take_screenshot", "Bash(git push:*)", "mcp__chrome-devtools-http__start_chrome_and_connect", "mcp__chrome-devtools-http__get_console_logs", "mcp__chrome-devtools-http__get_console_error_summary", "Bash(npm run lint)", "<PERSON><PERSON>(sed:*)", "Bash(npm install:*)", "mcp__context-7__resolve-library-id", "Bash(git rm:*)", "Bash(bash:*)", "<PERSON><PERSON>(docker inspect:*)", "<PERSON><PERSON>(chmod:*)", "Bash(docker build:*)", "<PERSON><PERSON>(docker compose:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(docker cp:*)", "Bash(ls:*)", "Bash(git stash:*)", "mcp__context-7__get-library-docs", "Bash(FALKORDB_URI=redis://localhost:6389 uv run scripts/test_node_merging.py)", "Bash(FALKORDB_HOST=localhost FALKORDB_PORT=6389 uv run scripts/test_node_merging.py)", "Bash(FALKORDB_HOST=localhost FALKORDB_PORT=6389 uv run scripts/test_node_merging_auto.py)", "Bash(FALKORDB_HOST=localhost FALKORDB_PORT=6389 uv run scripts/check_duplicates.py)", "Bash(FALKORDB_HOST=localhost FALKORDB_PORT=6389 uv run scripts/deduplicate_and_merge.py)", "Bash(FALKORDB_HOST=localhost FALKORDB_PORT=6389 uv run scripts/merge_duplicates_simple.py)", "Bash(FALKORDB_HOST=localhost FALKORDB_PORT=6389 uv run scripts/cleanup_all_duplicates.py)", "Bash(FALKORDB_HOST=localhost FALKORDB_PORT=6389 uv run scripts/clear_database.py)", "Bash(git commit:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(docker run:*)", "Bash(RUST_LOG=trace ./target/release/graphiti-centrality)", "<PERSON><PERSON>(md5sum:*)", "Bash(TEST_MODEL=\"phi4-mini-reasoning:latest\" python3 test_dedup_json_mode.py)", "Bash(docker network inspect:*)", "<PERSON><PERSON>(docker:*)", "Bash(npm run build:*)", "WebFetch(domain:react.dev)", "<PERSON><PERSON>(uv run:*)", "<PERSON><PERSON>(pkill:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(npm run dev:*)", "Bash(git merge:*)", "mcp__huly-mcp__huly_create_milestone", "mcp__chrome-devtools-http__navigate_to_url", "mcp__chrome-devtools-http__inspect_console_object", "mcp__playwright-http__browser_evaluate", "Bash(-p 8003:8000 )", "Bash(--network graphiti_graphiti_network )", "Bash(-e OPENAI_API_KEY=dummy_key_using_ollama_instead )", "Bash(-e USE_OLLAMA=true )", "Bash(-e OLLAMA_BASE_URL=http://*************:11434/v1 )", "Bash(-e OLLAMA_MODEL=gemma3:12b )", "Bash(-e OLLAMA_EMBEDDING_MODEL=mxbai-embed-large:latest )", "Bash(-e USE_OLLAMA_EMBEDDINGS=true )", "Bash(-e FALKORDB_HOST=falkordb )", "Bash(-e FALKORDB_PORT=6379 )", "Bash(-e FALKORDB_URI=redis://falkordb:6379 )", "Bash(-e USE_FALKORDB=true )", "Bash(-e USE_RUST_CENTRALITY=true )", "Bash(-e RUST_CENTRALITY_URL=http://graphiti-centrality-rs:3003 )", "Bash(-e RUST_SERVER_URL=http://graph-visualizer-rust:3000 )", "Bash(-e ENABLE_CACHE_INVALIDATION=true )", "Bash(-v /tmp/start-with-websockets.sh:/start-with-websockets.sh )", "<PERSON>sh(graphiti-graph:*)", "Bash(/start-with-websockets.sh)", "Bash(npx tsc:*)", "Bash(GRAPHITI_URL=http://*************:8003 docker-compose up -d webhook-receivers)", "<PERSON><PERSON>(cat:*)", "Bash(git rev-parse:*)"], "deny": []}}