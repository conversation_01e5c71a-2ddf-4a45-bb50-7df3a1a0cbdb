import{SvelteComponent as t,init as e,safe_not_equal as n,binding_callbacks as s,bind as o,element as i,create_component as r,space as l,set_style as c,attr as a,toggle_class as u,insert as d,mount_component as m,append as p,add_flush_callback as $,transition_in as f,transition_out as g,detach as h,destroy_component as x,createEventDispatcher as v,onMount as y,onDestroy as b,noop as w,empty as j,text as I,set_data as S,group_outros as k,check_outros as O,update_keyed_each as M,listen as _,is_function as L,run_all as T,destroy_block as E,outro_and_destroy_block as P}from'./../../ext/svelte/internal/index.mjs.js';import{ResizeObserver as A}from"@juggle/resize-observer";import C from"escape-string-regexp";import V from'./../../ext/@smui/list/dist/List.svelte.js';import H from'./../../ext/@smui/list/dist/Item.svelte.js';import N from'./../../ext/@smui/list/dist/Text.js';import'./../../ext/@smui/list/dist/PrimaryText.js';import'./../../ext/@smui/list/dist/SecondaryText.js';import'./../../ext/@smui/list/dist/Meta.js';import'./../../ext/@smui/list/dist/Group.js';import'./../../ext/@smui/list/dist/Subheader.js';import U from'./../../ext/@smui/textfield/dist/Textfield.svelte.js';import'./../../ext/@smui/textfield/dist/Prefix.js';import'./../../ext/@smui/textfield/dist/Suffix.js';import'./../../ext/@smui/textfield/dist/HelperLine.js';import D from'./../../ext/@smui/textfield/dist/icon/Icon.svelte.js';import R from'./../../ext/@smui/menu/dist/Menu.svelte.js';import'./../../ext/@smui/menu/dist/SelectionGroupIcon.js';import{Events as z}from"./types.js";import{defaultSearchConfig as q}from"./config.js";import B from"./search-svg.svelte.js";import"./smui.css.js";import"./search.css.js";function F(t,e,n){const s=t.slice();return s[53]=e[n],s[52]=n,s}function G(t,e,n){const s=t.slice();return s[50]=e[n],s[52]=n,s}function J(t){let e,n,s;return n=new B({}),{c(){e=i("div"),r(n.$$.fragment),a(e,"class","search-icon svelte-1xknafk")},m(t,o){d(t,e,o),m(n,e,null),s=!0},p:w,i(t){s||(f(n.$$.fragment,t),s=!0)},o(t){g(n.$$.fragment,t),s=!1},d(t){t&&h(e),x(n)}}}function Y(t){let e,n;return e=new D({props:{slot:"leadingIcon",$$slots:{default:[J]},$$scope:{ctx:t}}}),{c(){r(e.$$.fragment)},m(t,s){m(e,t,s),n=!0},p(t,n){const s={};16777216&n[1]&&(s.$$scope={dirty:n,ctx:t}),e.$set(s)},i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){g(e.$$.fragment,t),n=!1},d(t){x(e,t)}}}function K(t){let e,n,s=t[11].label+"";return{c(){e=i("div"),n=I(s),a(e,"class","cosmograph-search-accessor"),u(e,"active",t[2]),u(e,"disabled",!t[9])},m(t,s){d(t,e,s),p(e,n)},p(t,o){2048&o[0]&&s!==(s=t[11].label+"")&&S(n,s),4&o[0]&&u(e,"active",t[2]),512&o[0]&&u(e,"disabled",!t[9])},d(t){t&&h(e)}}}function Q(t){let e,n=t[11]&&K(t);return{c(){n&&n.c(),e=j()},m(t,s){n&&n.m(t,s),d(t,e,s)},p(t,s){t[11]?n?n.p(t,s):(n=K(t),n.c(),n.m(e.parentNode,e)):n&&(n.d(1),n=null)},d(t){n&&n.d(t),t&&h(e)}}}function W(t){let e,n;return e=new D({props:{role:"button",style:"display: flex;",slot:"trailingIcon",$$slots:{default:[Q]},$$scope:{ctx:t}}}),e.$on("SMUITextField:icon",t[14]),{c(){r(e.$$.fragment)},m(t,s){m(e,t,s),n=!0},p(t,n){const s={};2564&n[0]|16777216&n[1]&&(s.$$scope={dirty:n,ctx:t}),e.$set(s)},i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){g(e.$$.fragment,t),n=!1},d(t){x(e,t)}}}function X(t){let e,n,s;return n=new H({props:{$$slots:{default:[nt]},$$scope:{ctx:t}}}),{c(){e=i("div"),r(n.$$.fragment),c(e,"pointer-events","none")},m(t,o){d(t,e,o),m(n,e,null),s=!0},p(t,e){const s={};16777216&e[1]&&(s.$$scope={dirty:e,ctx:t}),n.$set(s)},i(t){s||(f(n.$$.fragment,t),s=!0)},o(t){g(n.$$.fragment,t),s=!1},d(t){t&&h(e),x(n)}}}function Z(t){let e,n=[],s=new Map,o=t[4];const i=t=>t[52];for(let e=0;e<o.length;e+=1){let r=F(t,o,e),l=i(r);s.set(l,n[e]=st(l,r))}return{c(){for(let t=0;t<n.length;t+=1)n[t].c();e=j()},m(t,s){for(let e=0;e<n.length;e+=1)n[e]&&n[e].m(t,s);d(t,e,s)},p(t,r){655376&r[0]&&(o=t[4],n=M(n,r,i,1,t,o,s,e.parentNode,E,st,e,F))},i:w,o:w,d(t){for(let e=0;e<n.length;e+=1)n[e].d(t);t&&h(e)}}}function tt(t){let e,n,s=t[1]?.accessors?.length&&ot(t);return{c(){s&&s.c(),e=j()},m(t,o){s&&s.m(t,o),d(t,e,o),n=!0},p(t,n){t[1]?.accessors?.length?s?(s.p(t,n),2&n[0]&&f(s,1)):(s=ot(t),s.c(),f(s,1),s.m(e.parentNode,e)):s&&(k(),g(s,1,1,(()=>{s=null})),O())},i(t){n||(f(s),n=!0)},o(t){g(s),n=!1},d(t){s&&s.d(t),t&&h(e)}}}function et(t){let e;return{c(){e=I("No matches found")},m(t,n){d(t,e,n)},d(t){t&&h(e)}}}function nt(t){let e,n;return e=new N({props:{$$slots:{default:[et]},$$scope:{ctx:t}}}),{c(){r(e.$$.fragment)},m(t,s){m(e,t,s),n=!0},p(t,n){const s={};16777216&n[1]&&(s.$$scope={dirty:n,ctx:t}),e.$set(s)},i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){g(e.$$.fragment,t),n=!1},d(t){x(e,t)}}}function st(t,e){let n,s,o,r,u,m=e[19](e[53])+"";return{key:t,first:null,c(){n=i("div"),s=i("div"),o=l(),a(n,"class","cosmograph-search-match"),c(n,"--margin-v",mt),c(n,"--margin-h",pt),this.first=n},m(t,i){d(t,n,i),p(n,s),s.innerHTML=m,p(n,o),r||(u=[_(n,"click",(function(){L(e[17](e[53]))&&e[17](e[53]).apply(this,arguments)})),_(n,"keydown",(function(){L(e[17](e[53]))&&e[17](e[53]).apply(this,arguments)}))],r=!0)},p(t,n){e=t,16&n[0]&&m!==(m=e[19](e[53])+"")&&(s.innerHTML=m)},d(t){t&&h(n),r=!1,T(u)}}}function ot(t){let e,n,s,o,a,u=[],p=new Map;n=new H({props:{$$slots:{default:[it]},$$scope:{ctx:t}}});let $=t[1]?.accessors;const v=t=>t[52];for(let e=0;e<$.length;e+=1){let n=G(t,$,e),s=v(n);p.set(s,u[e]=ct(s,n))}return{c(){e=i("div"),r(n.$$.fragment),s=l();for(let t=0;t<u.length;t+=1)u[t].c();o=j(),c(e,"pointer-events","none"),c(e,"font-size","10px",1)},m(t,i){d(t,e,i),m(n,e,null),d(t,s,i);for(let e=0;e<u.length;e+=1)u[e]&&u[e].m(t,i);d(t,o,i),a=!0},p(t,e){const s={};16777216&e[1]&&(s.$$scope={dirty:e,ctx:t}),n.$set(s),8194&e[0]&&($=t[1]?.accessors,k(),u=M(u,e,v,1,t,$,p,o.parentNode,P,ct,o,G),O())},i(t){if(!a){f(n.$$.fragment,t);for(let t=0;t<$.length;t+=1)f(u[t]);a=!0}},o(t){g(n.$$.fragment,t);for(let t=0;t<u.length;t+=1)g(u[t]);a=!1},d(t){t&&h(e),x(n),t&&h(s);for(let e=0;e<u.length;e+=1)u[e].d(t);t&&h(o)}}}function it(t){let e;return{c(){e=i("div"),e.textContent="select accessor to search",a(e,"class","cosmograph-search-result")},m(t,n){d(t,e,n)},p:w,d(t){t&&h(e)}}}function rt(t){let e,n=t[50].label+"";return{c(){e=I(n)},m(t,n){d(t,e,n)},p(t,s){2&s[0]&&n!==(n=t[50].label+"")&&S(e,n)},d(t){t&&h(e)}}}function lt(t){let e,n,s;return e=new N({props:{$$slots:{default:[rt]},$$scope:{ctx:t}}}),{c(){r(e.$$.fragment),n=l()},m(t,o){m(e,t,o),d(t,n,o),s=!0},p(t,n){const s={};2&n[0]|16777216&n[1]&&(s.$$scope={dirty:n,ctx:t}),e.$set(s)},i(t){s||(f(e.$$.fragment,t),s=!0)},o(t){g(e.$$.fragment,t),s=!1},d(t){x(e,t),t&&h(n)}}}function ct(t,e){let n,s,o;return s=new H({props:{$$slots:{default:[lt]},$$scope:{ctx:e}}}),s.$on("click",(function(){L(e[13](e[50],e[52]))&&e[13](e[50],e[52]).apply(this,arguments)})),{key:t,first:null,c(){n=j(),r(s.$$.fragment),this.first=n},m(t,e){d(t,n,e),m(s,t,e),o=!0},p(t,n){e=t;const o={};2&n[0]|16777216&n[1]&&(o.$$scope={dirty:n,ctx:e}),s.$set(o)},i(t){o||(f(s.$$.fragment,t),o=!0)},o(t){g(s.$$.fragment,t),o=!1},d(t){t&&h(n),x(s,t)}}}function at(t){let e,n,s,o;const r=[tt,Z,X],l=[];function c(t,e){return t[2]?0:t[4].length?1:2}return n=c(t),s=l[n]=r[n](t),{c(){e=i("div"),s.c()},m(s,i){d(s,e,i),l[n].m(e,null),t[28](e),o=!0},p(t,o){let i=n;n=c(t),n===i?l[n].p(t,o):(k(),g(l[i],1,1,(()=>{l[i]=null})),O(),s=l[n],s?s.p(t,o):(s=l[n]=r[n](t),s.c()),f(s,1),s.m(e,null))},i(t){o||(f(s),o=!0)},o(t){g(s),o=!1},d(s){s&&h(e),l[n].d(),t[28](null)}}}function ut(t){let e,n;return e=new V({props:{style:"max-height: "+t[8]+"px; transition: max-height 0.1s linear;",$$slots:{default:[at]},$$scope:{ctx:t}}}),{c(){r(e.$$.fragment)},m(t,s){m(e,t,s),n=!0},p(t,n){const s={};256&n[0]&&(s.style="max-height: "+t[8]+"px; transition: max-height 0.1s linear;"),86&n[0]|16777216&n[1]&&(s.$$scope={dirty:n,ctx:t}),e.$set(s)},i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){g(e.$$.fragment,t),n=!1},d(t){x(e,t)}}}function dt(t){let e,n,v,y,b,w,j,I,S;function k(e){t[26](e)}function O(e){t[27](e)}let M={style:"opacity: "+(t[1].isDisabled?.5:1),label:t[1].placeholder,$$slots:{trailingIcon:[W],leadingIcon:[Y]},$$scope:{ctx:t}};function _(e){t[29](e)}void 0!==t[0]&&(M.value=t[0]),void 0!==t[12]&&(M.disabled=t[12]),n=new U({props:M}),t[25](n),s.push((()=>o(n,"value",k))),s.push((()=>o(n,"disabled",O))),n.$on("click",t[18]),n.$on("focus",t[20]),n.$on("input",t[16]),n.$on("keydown",t[15]);let L={style:"width: 100%; bottom: initial;",$$slots:{default:[ut]},$$scope:{ctx:t}};return void 0!==t[3]&&(L.open=t[3]),j=new R({props:L}),s.push((()=>o(j,"open",_))),t[30](j),{c(){e=i("div"),r(n.$$.fragment),b=l(),w=i("div"),r(j.$$.fragment),c(w,"position","relative"),a(w,"class","svelte-1xknafk"),u(w,"openListUpwards",t[1].openListUpwards),u(w,"accessors",t[2]),a(e,"class","search svelte-1xknafk")},m(s,o){d(s,e,o),m(n,e,null),p(e,b),p(e,w),m(j,w,null),t[31](e),S=!0},p(t,e){const s={};2&e[0]&&(s.style="opacity: "+(t[1].isDisabled?.5:1)),2&e[0]&&(s.label=t[1].placeholder),2564&e[0]|16777216&e[1]&&(s.$$scope={dirty:e,ctx:t}),!v&&1&e[0]&&(v=!0,s.value=t[0],$((()=>v=!1))),!y&&4096&e[0]&&(y=!0,s.disabled=t[12],$((()=>y=!1))),n.$set(s);const o={};342&e[0]|16777216&e[1]&&(o.$$scope={dirty:e,ctx:t}),!I&&8&e[0]&&(I=!0,o.open=t[3],$((()=>I=!1))),j.$set(o),(!S||2&e[0])&&u(w,"openListUpwards",t[1].openListUpwards),(!S||4&e[0])&&u(w,"accessors",t[2])},i(t){S||(f(n.$$.fragment,t),f(j.$$.fragment,t),S=!0)},o(t){g(n.$$.fragment,t),g(j.$$.fragment,t),S=!1},d(s){s&&h(e),t[25](null),x(n),t[30](null),x(j),t[31](null)}}}const mt=4,pt=12;function $t(t,e,n){let o;var i,r;const l=v();let c,a,u,d,m,p,{config:$}=e,{data:f=[]}=e,{textInput:g=""}=e,h=!0,x=!1,w=!1,j=[];const I=new Set,S=new DOMParser;let k;const O=t=>{if(!f)return;if(!t.trim())return void n(4,j=[]);let e=0;const s=new RegExp(C(t),"i"),o=t=>String(k.accessor(t));n(4,j=f.filter((t=>{if($.limitSuggestions&&e>=$.limitSuggestions)return!1;const n=o(t).match(s);return n&&(e+=1),n}))),j.length>0&&j.sort(((e,n)=>{const s=o(e).toLowerCase(),i=o(n).toLowerCase(),r=t.toLowerCase();if(s===r&&i!==r)return-1;if(i===r&&s!==r)return 1;const l=s.indexOf(r),c=i.indexOf(r);return l!==c?l-c:s.localeCompare(i)})),l(z.Input,j)},M=t=>{return RegExp.prototype.test.bind(/(<([^>]+)>)/i)(t)?(e=t,S.parseFromString(e,"text/html").documentElement.textContent||""):t;var e},_=t=>t.replace(/[&<>]/g,(t=>({"&":"&amp;","<":"&lt;",">":"&gt;"}[t]||t))),L=(t,e)=>{const n=M(t),s=$&&$.truncateValues?$.truncateValues:n.length,o=(t=>new RegExp(C(t),"i"))(g),i=e?((t,e)=>t.search(e))(n,o):-1;if(-1===i)return n.substring(0,+s)+(n.length>s?"...":"");const{startPosition:r,endPosition:l}=((t,e,n)=>{let s=Math.max(0,t-Math.floor(e/2));const o=Math.min(n,s+e);return o-t<e/2&&(s=Math.max(0,o-e)),{startPosition:s,endPosition:o}})(i,+s,n.length),c=n.substring(r,l),a=r>0?"...":"",u=l<n.length?"...":"";return`${a}${e?c.replace(o,(t=>`<mark>${t}</mark>`)):c}${u}`},T=()=>{setTimeout((()=>{var t;let e,s=0;const o=null!==(t=$.maxVisibleItems)&&void 0!==t?t:q.maxVisibleItems;a.querySelectorAll(`.cosmograph-search-match:nth-child(-n+${o})`).forEach((t=>{s+=t.offsetHeight})),s=j.length?s+6:46,e=$.openListUpwards?u.getBoundingClientRect().top-window.scrollY-60:window.innerHeight-u.getBoundingClientRect().bottom-60,s>e&&(s=e),n(8,m=s)}),0)},E=()=>{setTimeout((()=>{var t;let e=0;const s=null!==(t=$.maxVisibleItems)&&void 0!==t?t:q.maxVisibleItems;a.querySelectorAll(`li:nth-child(-n+${s})`).forEach((t=>{e+=t.offsetHeight})),n(8,m=e+24)}),0)};y((()=>{d=new A((()=>{d&&a&&(j&&T(),x&&E())})),d.observe(u)})),b((()=>{d.disconnect()}));return t.$$set=t=>{"config"in t&&n(1,$=t.config),"data"in t&&n(21,f=t.data),"textInput"in t&&n(0,g=t.textInput)},t.$$.update=()=>{if(25165826&t.$$.dirty[0]){n(11,k=(()=>{var t,e,n;return null!=$.activeAccessorIndex&&(null===(t=$.accessors)||void 0===t?void 0:t[$.activeAccessorIndex])?$.accessors[$.activeAccessorIndex]:k&&$.accessors?$.accessors.find((t=>t===k))||(null===(e=$.accessors)||void 0===e?void 0:e[0]):null===(n=$.accessors)||void 0===n?void 0:n[0]})());const t=null!==n(24,r=null===n(23,i=$.accessors)||void 0===i?void 0:i.length)&&void 0!==r?r:0;n(9,h=t>1)}12&t.$$.dirty[0]&&x&&!w&&setTimeout((()=>{n(2,x=!1)}),100),2097158&t.$$.dirty[0]&&n(12,o=!f.length||x||!!$.isDisabled),16&t.$$.dirty[0]&&j&&T(),4&t.$$.dirty[0]&&x&&E()},[g,$,x,w,j,c,a,u,m,h,p,k,o,(t,e)=>{void 0===$.activeAccessorIndex&&n(11,k=t),null==p||p.setOpen(!1),setTimeout((()=>{n(2,x=!1)}),100),l(z.AccessorSelect,{index:e,accessor:k})},()=>{h&&(n(0,g=""),null==p||p.setOpen(!1),setTimeout((()=>{null==p||p.setOpen(!0),n(2,x=!0)}),100))},t=>{"Enter"===t.key&&void 0!==$.minMatch&&g.length>=$.minMatch&&l(z.Enter,{textInput:g,accessor:k})},t=>{const e=null==t?void 0:t.target;x||(void 0!==$.minMatch&&e.value.length<$.minMatch?null==p||p.setOpen(!1):(null==p||p.setOpen(!0),O(e.value)))},t=>e=>{null!==document.activeElement&&document.activeElement.blur(),p.setOpen(!1),I.size>=5&&I.delete(I.values().next().value),I.add(t),l(z.Select,t)},()=>{setTimeout((()=>{x||w||0!==g.length||(I.size?n(4,j=Array.from(I)):f.length&&n(4,j=f.slice(0,$.maxVisibleItems)),p.setOpen(!0))}),110)},t=>(t=>{var e;const{[k.label]:n,...s}=t;if(Object.keys(s).length>0){const t=null!==(e=$.matchPalette)&&void 0!==e?e:q.matchPalette,o=e=>`color: ${t[e%t.length]}`,i=Object.entries(s).map((([t,e],n)=>{const s=L("object"==typeof e?JSON.stringify(e):String(e));return`<span style="${o(n)}"><t>·</t><b>${_(t)}</b>: ${s}</span>`}));return`\n      <span class="cosmograph-search-first-field"><b>${_(k.label)}</b>:</span>\n      ${L(String(n),!0)}\n      <div class='cosmograph-search-result'>\n        ${i.join("")} \n      </div>\n    `}return L(String(n),!0)})((t=>{const e={},n=Object.keys(t),s=k.accessor(t);if(s&&(e[k.label]=s),!$.ordering||!$.ordering.order&&!$.ordering.include){const s=Object.entries(t).findIndex((([t,e])=>k.accessor({[t]:e})));-1!==s&&n.splice(s,1);for(const s of n)e[s]=t[s];return e}const o=$.ordering.order||[];let i=$.ordering.include?new Set($.ordering.include):null;if(i||(i=new Set(n)),o.length>0)for(const n of o)n in t&&(e[n]=t[n]);for(const n in t)!Object.prototype.hasOwnProperty.call(e,n)&&i.has(n)&&(e[n]=t[n]);return e})(t)),t=>{t.preventDefault()},f,t=>{null==p||p.setOpen(t)},i,r,function(t){s[t?"unshift":"push"]((()=>{c=t,n(5,c)}))},function(t){g=t,n(0,g)},function(t){o=t,n(12,o),n(21,f),n(2,x),n(1,$),n(3,w)},function(t){s[t?"unshift":"push"]((()=>{a=t,n(6,a)}))},function(t){w=t,n(3,w)},function(t){s[t?"unshift":"push"]((()=>{p=t,n(10,p)}))},function(t){s[t?"unshift":"push"]((()=>{u=t,n(7,u)}))}]}class ft extends t{constructor(t){super(),e(this,t,$t,dt,n,{config:1,data:21,textInput:0,setListState:22},null,[-1,-1])}get setListState(){return this.$$.ctx[22]}}export{ft as default};
//# sourceMappingURL=search.svelte.js.map
