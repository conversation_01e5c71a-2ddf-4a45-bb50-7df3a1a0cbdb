{"version": 3, "file": "foundation.js", "sources": ["../../../../../../node_modules/@material/base/foundation.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar MDCFoundation = /** @class */ (function () {\n    function MDCFoundation(adapter) {\n        if (adapter === void 0) { adapter = {}; }\n        this.adapter = adapter;\n    }\n    Object.defineProperty(MDCFoundation, \"cssClasses\", {\n        get: function () {\n            // Classes extending MDCFoundation should implement this method to return an object which exports every\n            // CSS class the foundation class needs as a property. e.g. {ACTIVE: 'mdc-component--active'}\n            return {};\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MDCFoundation, \"strings\", {\n        get: function () {\n            // Classes extending MDCFoundation should implement this method to return an object which exports all\n            // semantic strings as constants. e.g. {ARIA_ROLE: 'tablist'}\n            return {};\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MDCFoundation, \"numbers\", {\n        get: function () {\n            // Classes extending MDCFoundation should implement this method to return an object which exports all\n            // of its semantic numbers as constants. e.g. {ANIMATION_DELAY_MS: 350}\n            return {};\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(MDCFoundation, \"defaultAdapter\", {\n        get: function () {\n            // Classes extending MDCFoundation may choose to implement this getter in order to provide a convenient\n            // way of viewing the necessary methods of an adapter. In the future, this could also be used for adapter\n            // validation.\n            return {};\n        },\n        enumerable: false,\n        configurable: true\n    });\n    MDCFoundation.prototype.init = function () {\n        // Subclasses should override this method to perform initialization routines (registering events, etc.)\n    };\n    MDCFoundation.prototype.destroy = function () {\n        // Subclasses should override this method to perform de-initialization routines (de-registering events, etc.)\n    };\n    return MDCFoundation;\n}());\nexport { MDCFoundation };\n// tslint:disable-next-line:no-default-export Needed for backward compatibility with MDC Web v0.44.0 and earlier.\nexport default MDCFoundation;\n//# sourceMappingURL=foundation.js.map"], "names": ["MDCFoundation", "adapter", "this", "Object", "defineProperty", "get", "enumerable", "configurable", "prototype", "init", "destroy"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAsBG,IAACA,EAA+B,WAC/B,SAASA,EAAcC,QACH,IAAZA,IAAsBA,EAAU,CAAE,GACtCC,KAAKD,QAAUA,CAClB,CA4CD,OA3CAE,OAAOC,eAAeJ,EAAe,aAAc,CAC/CK,IAAK,WAGD,MAAO,EACV,EACDC,YAAY,EACZC,cAAc,IAElBJ,OAAOC,eAAeJ,EAAe,UAAW,CAC5CK,IAAK,WAGD,MAAO,EACV,EACDC,YAAY,EACZC,cAAc,IAElBJ,OAAOC,eAAeJ,EAAe,UAAW,CAC5CK,IAAK,WAGD,MAAO,EACV,EACDC,YAAY,EACZC,cAAc,IAElBJ,OAAOC,eAAeJ,EAAe,iBAAkB,CACnDK,IAAK,WAID,MAAO,EACV,EACDC,YAAY,EACZC,cAAc,IAElBP,EAAcQ,UAAUC,KAAO,WAEnC,EACIT,EAAcQ,UAAUE,QAAU,WAEtC,EACWV,CACX"}