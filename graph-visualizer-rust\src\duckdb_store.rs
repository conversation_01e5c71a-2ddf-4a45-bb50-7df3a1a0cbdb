use anyhow::Result;
use arrow::array::{ArrayRef, Float64<PERSON>rray, Record<PERSON>atch, StringArray, UInt32Array};
use arrow::datatypes::{DataType, Field, Schema};
use arrow_schema::SchemaRef;
use chrono::DateTime;
use duckdb::{params, Connection};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

use crate::{Edge, Node};

#[derive(Clone)]
pub struct DuckDBStore {
    conn: Arc<Mutex<Connection>>,
    schema_nodes: SchemaRef,
    schema_edges: SchemaRef,
    update_queue: Arc<RwLock<UpdateQueue>>,
}

#[derive(Default)]
struct UpdateQueue {
    nodes_to_add: Vec<Node>,
    edges_to_add: Vec<Edge>,
    nodes_to_update: HashMap<String, Node>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct GraphUpdate {
    pub operation: UpdateOperation,
    pub nodes: Option<Vec<Node>>,
    pub edges: Option<Vec<Edge>>,
    pub timestamp: u64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "snake_case")]
pub enum UpdateOperation {
    AddNodes,
    AddEdges,
    UpdateNodes,
    DeleteNodes,
    DeleteEdges,
}

impl DuckDBStore {
    pub fn new() -> Result<Self> {
        let conn = Connection::open_in_memory()?;
        
        // Create node schema for Arrow
        let schema_nodes = Arc::new(Schema::new(vec![
            Field::new("id", DataType::Utf8, false),
            Field::new("idx", DataType::UInt32, false), // Required by Cosmograph v2
            Field::new("label", DataType::Utf8, false),
            Field::new("node_type", DataType::Utf8, false),
            Field::new("summary", DataType::Utf8, true),
            Field::new("degree_centrality", DataType::Float64, true),
            Field::new("x", DataType::Float64, true),
            Field::new("y", DataType::Float64, true),
            Field::new("color", DataType::Utf8, true),
            Field::new("size", DataType::Float64, true),
            Field::new("created_at_timestamp", DataType::Float64, true), // For timeline
            Field::new("cluster", DataType::Utf8, true), // For clustering
            Field::new("clusterStrength", DataType::Float64, true), // Clustering strength
        ]));
        
        // Create edge schema for Arrow
        let schema_edges = Arc::new(Schema::new(vec![
            Field::new("source", DataType::Utf8, false),
            Field::new("sourceidx", DataType::UInt32, false), // Required by Cosmograph v2
            Field::new("target", DataType::Utf8, false),
            Field::new("targetidx", DataType::UInt32, false), // Required by Cosmograph v2
            Field::new("edge_type", DataType::Utf8, false),
            Field::new("weight", DataType::Float64, false),
            Field::new("color", DataType::Utf8, true),
        ]));
        
        // Create tables
        conn.execute(
            "CREATE TABLE nodes (
                id VARCHAR PRIMARY KEY,
                idx INTEGER NOT NULL,
                label VARCHAR NOT NULL,
                node_type VARCHAR NOT NULL,
                summary VARCHAR,
                degree_centrality DOUBLE,
                x DOUBLE,
                y DOUBLE,
                color VARCHAR,
                size DOUBLE,
                created_at_timestamp DOUBLE,
                cluster VARCHAR,
                clusterStrength DOUBLE
            )",
            params![],
        )?;
        
        conn.execute(
            "CREATE TABLE edges (
                source VARCHAR NOT NULL,
                sourceidx INTEGER NOT NULL,
                target VARCHAR NOT NULL,
                targetidx INTEGER NOT NULL,
                edge_type VARCHAR NOT NULL,
                weight DOUBLE NOT NULL DEFAULT 1.0,
                color VARCHAR,
                PRIMARY KEY (source, target, edge_type)
            )",
            params![],
        )?;
        
        // Create indexes for performance
        conn.execute("CREATE INDEX idx_nodes_type ON nodes(node_type)", params![])?;
        conn.execute("CREATE INDEX idx_nodes_idx ON nodes(idx)", params![])?;
        conn.execute("CREATE INDEX idx_edges_source ON edges(sourceidx)", params![])?;
        conn.execute("CREATE INDEX idx_edges_target ON edges(targetidx)", params![])?;
        
        info!("DuckDB store initialized with in-memory database");
        
        Ok(Self {
            conn: Arc::new(Mutex::new(conn)),
            schema_nodes,
            schema_edges,
            update_queue: Arc::new(RwLock::new(UpdateQueue::default())),
        })
    }
    
    pub async fn load_initial_data(&self, nodes: Vec<Node>, edges: Vec<Edge>) -> Result<()> {
        info!("Loading initial data: {} nodes, {} edges", nodes.len(), edges.len());
        
        let mut conn = self.conn.lock().unwrap();
        let tx = conn.transaction()?;
        
        // Insert nodes with indices - use INSERT OR REPLACE to handle duplicates
        let stmt_node = "INSERT OR REPLACE INTO nodes (id, idx, label, node_type, summary, degree_centrality, x, y, color, size, created_at_timestamp, cluster, clusterStrength) 
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        let mut node_to_idx = HashMap::new();
        
        for (idx, node) in nodes.iter().enumerate() {
            let degree = node.properties.get("degree_centrality")
                .and_then(|v| v.as_f64())
                .unwrap_or(0.0);
            
            let color = self.get_node_color(&node.node_type);
            let size = 4.0 + (degree * 20.0); // Size based on centrality
            
            // Default clustering by node_type with strength 0.7
            let cluster = node.node_type.clone();
            let cluster_strength = 0.7;
            
            // Use real timestamp from created_at if available, otherwise generate synthetic one
            let timestamp = if let Some(created_str) = node.properties.get("created_at")
                .and_then(|v| v.as_str()) {
                // Parse ISO date to milliseconds
                DateTime::parse_from_rfc3339(created_str)
                    .map(|dt| dt.timestamp_millis() as f64)
                    .unwrap_or_else(|e| {
                        warn!("Failed to parse created_at '{}': {}", created_str, e);
                        (idx as f64) * 86400000.0 // Fallback to synthetic
                    })
            } else {
                debug!("Node {} has no created_at, using synthetic timestamp", node.id);
                (idx as f64) * 86400000.0 // Fallback to synthetic timestamp
            };
            
            tx.execute(
                stmt_node,
                params![
                    &node.id,
                    idx as u32,
                    &node.label,
                    &node.node_type,
                    &node.summary,
                    degree,
                    Option::<f64>::None, // x - will be computed by layout
                    Option::<f64>::None, // y - will be computed by layout
                    color,
                    size,
                    timestamp,
                    cluster,
                    cluster_strength
                ],
            )?;
            
            node_to_idx.insert(node.id.clone(), idx as u32);
        }
        
        // Insert edges with indices
        let stmt_edge = "INSERT OR IGNORE INTO edges (source, sourceidx, target, targetidx, edge_type, weight, color) 
                         VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        for edge in edges.iter() {
            if let (Some(&source_idx), Some(&target_idx)) = 
                (node_to_idx.get(&edge.from), node_to_idx.get(&edge.to)) {
                
                let color = self.get_edge_color(&edge.edge_type);
                
                tx.execute(
                    stmt_edge,
                    params![
                        &edge.from,
                        source_idx,
                        &edge.to,
                        target_idx,
                        &edge.edge_type,
                        edge.weight,
                        color
                    ],
                )?;
            }
        }
        
        tx.commit()?;
        
        info!("Initial data loaded successfully");
        Ok(())
    }
    
    pub async fn get_nodes_as_arrow(&self) -> Result<RecordBatch> {
        let conn = self.conn.lock().unwrap();
        
        let mut stmt = conn.prepare(
            "SELECT id, idx, label, node_type, summary, degree_centrality, x, y, color, size, created_at_timestamp, cluster, clusterStrength 
             FROM nodes 
             ORDER BY idx"
        )?;
        
        let mut ids = Vec::new();
        let mut indices = Vec::new();
        let mut labels = Vec::new();
        let mut node_types = Vec::new();
        let mut summaries = Vec::new();
        let mut degrees = Vec::new();
        let mut xs = Vec::new();
        let mut ys = Vec::new();
        let mut colors = Vec::new();
        let mut sizes = Vec::new();
        let mut timestamps = Vec::new();
        let mut clusters = Vec::new();
        let mut cluster_strengths = Vec::new();
        
        let rows = stmt.query_map(params![], |row| {
            Ok((
                row.get::<_, String>(0)?,     // id
                row.get::<_, u32>(1)?,        // idx
                row.get::<_, String>(2)?,     // label
                row.get::<_, String>(3)?,     // node_type
                row.get::<_, Option<String>>(4)?, // summary
                row.get::<_, Option<f64>>(5)?,    // degree_centrality
                row.get::<_, Option<f64>>(6)?,    // x
                row.get::<_, Option<f64>>(7)?,    // y
                row.get::<_, Option<String>>(8)?, // color
                row.get::<_, Option<f64>>(9)?,    // size
                row.get::<_, Option<f64>>(10)?,   // created_at_timestamp
                row.get::<_, Option<String>>(11)?, // cluster
                row.get::<_, Option<f64>>(12)?,   // clusterStrength
            ))
        })?;
        
        for row in rows {
            let (id, idx, label, node_type, summary, degree, x, y, color, size, timestamp, cluster, cluster_strength) = row?;
            ids.push(id);
            indices.push(idx);
            labels.push(label);
            node_types.push(node_type);
            summaries.push(summary);
            degrees.push(degree);
            xs.push(x);
            ys.push(y);
            colors.push(color);
            sizes.push(size);
            timestamps.push(timestamp);
            clusters.push(cluster);
            cluster_strengths.push(cluster_strength);
        }
        
        let batch = RecordBatch::try_new(
            self.schema_nodes.clone(),
            vec![
                Arc::new(StringArray::from(ids)) as ArrayRef,
                Arc::new(UInt32Array::from(indices)) as ArrayRef,
                Arc::new(StringArray::from(labels)) as ArrayRef,
                Arc::new(StringArray::from(node_types)) as ArrayRef,
                Arc::new(StringArray::from(summaries)) as ArrayRef,
                Arc::new(Float64Array::from(degrees)) as ArrayRef,
                Arc::new(Float64Array::from(xs)) as ArrayRef,
                Arc::new(Float64Array::from(ys)) as ArrayRef,
                Arc::new(StringArray::from(colors)) as ArrayRef,
                Arc::new(Float64Array::from(sizes)) as ArrayRef,
                Arc::new(Float64Array::from(timestamps)) as ArrayRef,
                Arc::new(StringArray::from(clusters)) as ArrayRef,
                Arc::new(Float64Array::from(cluster_strengths)) as ArrayRef,
            ],
        )?;
        
        Ok(batch)
    }
    
    pub async fn get_edges_as_arrow(&self) -> Result<RecordBatch> {
        let conn = self.conn.lock().unwrap();
        
        let mut stmt = conn.prepare(
            "SELECT source, sourceidx, target, targetidx, edge_type, weight, color 
             FROM edges 
             ORDER BY sourceidx, targetidx"
        )?;
        
        let mut sources = Vec::new();
        let mut source_indices = Vec::new();
        let mut targets = Vec::new();
        let mut target_indices = Vec::new();
        let mut edge_types = Vec::new();
        let mut weights = Vec::new();
        let mut colors = Vec::new();
        
        let rows = stmt.query_map(params![], |row| {
            Ok((
                row.get::<_, String>(0)?,     // source
                row.get::<_, u32>(1)?,        // sourceidx
                row.get::<_, String>(2)?,     // target
                row.get::<_, u32>(3)?,        // targetidx
                row.get::<_, String>(4)?,     // edge_type
                row.get::<_, f64>(5)?,        // weight
                row.get::<_, Option<String>>(6)?, // color
            ))
        })?;
        
        for row in rows {
            let (source, source_idx, target, target_idx, edge_type, weight, color) = row?;
            sources.push(source);
            source_indices.push(source_idx);
            targets.push(target);
            target_indices.push(target_idx);
            edge_types.push(edge_type);
            weights.push(weight);
            colors.push(color);
        }
        
        let batch = RecordBatch::try_new(
            self.schema_edges.clone(),
            vec![
                Arc::new(StringArray::from(sources)) as ArrayRef,
                Arc::new(UInt32Array::from(source_indices)) as ArrayRef,
                Arc::new(StringArray::from(targets)) as ArrayRef,
                Arc::new(UInt32Array::from(target_indices)) as ArrayRef,
                Arc::new(StringArray::from(edge_types)) as ArrayRef,
                Arc::new(Float64Array::from(weights)) as ArrayRef,
                Arc::new(StringArray::from(colors)) as ArrayRef,
            ],
        )?;
        
        Ok(batch)
    }
    
    pub async fn queue_node_update(&self, node: Node) {
        let mut queue = self.update_queue.write().await;
        queue.nodes_to_update.insert(node.id.clone(), node);
    }
    
    pub async fn queue_nodes(&self, nodes: Vec<Node>) {
        let mut queue = self.update_queue.write().await;
        queue.nodes_to_add.extend(nodes);
    }
    
    pub async fn queue_edges(&self, edges: Vec<Edge>) {
        let mut queue = self.update_queue.write().await;
        queue.edges_to_add.extend(edges);
    }
    
    pub async fn process_updates(&self) -> Result<Option<GraphUpdate>> {
        let mut queue = self.update_queue.write().await;
        
        if queue.nodes_to_add.is_empty() && 
           queue.edges_to_add.is_empty() && 
           queue.nodes_to_update.is_empty() {
            return Ok(None);
        }
        
        let mut conn = self.conn.lock().unwrap();
        let tx = conn.transaction()?;
        
        let mut update = GraphUpdate {
            operation: UpdateOperation::AddNodes,
            nodes: None,
            edges: None,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
        };
        
        // Process new nodes
        if !queue.nodes_to_add.is_empty() {
            debug!("Processing {} new nodes", queue.nodes_to_add.len());
            
            // Get current max index
            let max_idx: u32 = tx.query_row(
                "SELECT COALESCE(MAX(idx), -1) FROM nodes",
                params![],
                |row| row.get(0)
            )?;
            
            let mut start_idx = max_idx + 1;
            let new_nodes = queue.nodes_to_add.drain(..).collect::<Vec<_>>();
            
            for node in &new_nodes {
                let degree = node.properties.get("degree_centrality")
                    .and_then(|v| v.as_f64())
                    .unwrap_or(0.0);
                
                let color = self.get_node_color(&node.node_type);
                let size = 4.0 + (degree * 20.0);
                
                // Default clustering by node_type with strength 0.7
                let cluster = node.node_type.clone();
                let cluster_strength = 0.7;
                
                // Parse real timestamp from created_at if available
                let timestamp = if let Some(created_str) = node.properties.get("created_at")
                    .and_then(|v| v.as_str()) {
                    DateTime::parse_from_rfc3339(created_str)
                        .map(|dt| dt.timestamp_millis() as f64)
                        .unwrap_or((start_idx as f64) * 86400000.0)
                } else {
                    (start_idx as f64) * 86400000.0
                };

                tx.execute(
                    "INSERT OR REPLACE INTO nodes (id, idx, label, node_type, summary, degree_centrality, x, y, color, size, created_at_timestamp, cluster, clusterStrength) 
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    params![
                        &node.id,
                        start_idx,
                        &node.label,
                        &node.node_type,
                        &node.summary,
                        degree,
                        Option::<f64>::None,
                        Option::<f64>::None,
                        color,
                        size,
                        timestamp,
                        cluster,
                        cluster_strength
                    ],
                )?;
                
                start_idx += 1;
            }
            
            update.operation = UpdateOperation::AddNodes;
            update.nodes = Some(new_nodes);
        }
        
        // Process node updates
        if !queue.nodes_to_update.is_empty() {
            debug!("Processing {} node updates", queue.nodes_to_update.len());
            
            let updates = queue.nodes_to_update.drain().collect::<Vec<_>>();
            
            for (_, node) in &updates {
                tx.execute(
                    "UPDATE nodes SET label = ?, summary = ? WHERE id = ?",
                    params![&node.label, &node.summary, &node.id],
                )?;
            }
            
            if update.nodes.is_none() {
                update.operation = UpdateOperation::UpdateNodes;
                update.nodes = Some(updates.into_iter().map(|(_, n)| n).collect());
            }
        }
        
        // Process new edges
        if !queue.edges_to_add.is_empty() {
            debug!("Processing {} new edges", queue.edges_to_add.len());
            
            let new_edges = queue.edges_to_add.drain(..).collect::<Vec<_>>();
            
            for edge in &new_edges {
                // Get indices for source and target
                let source_idx: Option<u32> = tx.query_row(
                    "SELECT idx FROM nodes WHERE id = ?",
                    params![&edge.from],
                    |row| row.get(0)
                ).ok();
                
                let target_idx: Option<u32> = tx.query_row(
                    "SELECT idx FROM nodes WHERE id = ?",
                    params![&edge.to],
                    |row| row.get(0)
                ).ok();
                
                if let (Some(src_idx), Some(tgt_idx)) = (source_idx, target_idx) {
                    let color = self.get_edge_color(&edge.edge_type);
                    
                    tx.execute(
                        "INSERT OR IGNORE INTO edges (source, sourceidx, target, targetidx, edge_type, weight, color) 
                         VALUES (?, ?, ?, ?, ?, ?, ?)",
                        params![
                            &edge.from,
                            src_idx,
                            &edge.to,
                            tgt_idx,
                            &edge.edge_type,
                            edge.weight,
                            color
                        ],
                    )?;
                }
            }
            
            if update.nodes.is_none() {
                update.operation = UpdateOperation::AddEdges;
            }
            update.edges = Some(new_edges);
        }
        
        tx.commit()?;
        
        Ok(Some(update))
    }
    
    pub async fn get_stats(&self) -> Result<(usize, usize)> {
        let conn = self.conn.lock().unwrap();
        
        let node_count: i64 = conn.query_row(
            "SELECT COUNT(*) FROM nodes",
            params![],
            |row| row.get(0)
        )?;
        
        let edge_count: i64 = conn.query_row(
            "SELECT COUNT(*) FROM edges",
            params![],
            |row| row.get(0)
        )?;
        
        Ok((node_count as usize, edge_count as usize))
    }
    
    fn get_node_color(&self, node_type: &str) -> String {
        match node_type {
            "EntityNode" => "#4CAF50".to_string(),
            "EpisodicNode" => "#2196F3".to_string(),
            "GroupNode" => "#FF9800".to_string(),
            _ => "#9E9E9E".to_string(),
        }
    }
    
    fn get_edge_color(&self, edge_type: &str) -> String {
        match edge_type {
            "RELATES_TO" => "#666666".to_string(),
            "MENTIONS" => "#999999".to_string(),
            "HAS_MEMBER" => "#FF9800".to_string(),
            _ => "#CCCCCC".to_string(),
        }
    }
}