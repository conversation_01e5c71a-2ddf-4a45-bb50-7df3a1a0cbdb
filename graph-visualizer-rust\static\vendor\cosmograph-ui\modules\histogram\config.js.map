{"version": 3, "file": "config.js", "sources": ["../../../src/modules/histogram/config.ts"], "sourcesContent": ["import { Config } from '../../utils'\nimport type { BarData, Padding } from './types'\n\nexport const DEFAULT_PADDING: Padding = {\n  top: 5,\n  left: 5,\n  bottom: 1,\n  right: 5,\n}\n\nexport class HistogramConfig extends Config implements HistogramConfigInterface {\n  padding = DEFAULT_PADDING\n  barsCount = 50\n  barPadding = 0.1\n  minBarHeight = 2\n  selectionRadius = 3\n  selectionPadding = 8\n  barCount = 30\n  dataStep = undefined\n  barRadius = 1\n  barTopMargin = 7\n  labelSideMargin = 3\n  formatter = undefined\n  allowSelection = true\n  stickySelection = true\n  events: HistogramEvents = {\n    onBrush: undefined,\n    onBarHover: undefined,\n  }\n}\n\nexport type HistogramConfigInterface = {\n  /** `padding`: Padding for the `Histogram` component. */\n  padding?: Padding;\n  /** `minBarHeight`: Minimum height for each bar in the `Histogram` component. Default: `2` */\n  minBarHeight?: number;\n  /** `selectionPadding`: Padding for the data selection brush. Set in pixels. Default: `8` */\n  selectionPadding?: number;\n  /** `selectionRadius`: Radius of the data selection brush. Default: `3` */\n  selectionRadius?: number;\n  /** `barPadding`: Padding between each bar. Set in percent of bar width from 0 (as 0% of the bar width) to 1 (as 100% of the bar width). Default: `0.1`. */\n  barPadding?: number;\n  /** `barRadius`: Corners roundness of each bar in the `Histogram`. Set in pixels. Default: `1` */\n  barRadius?: number;\n  /** `barCount`: Number of bars to be displayed in the `Histogram`. Ignored if `dataStep` is set. Default: `100` */\n  barCount?: number;\n  /** `barTopMargin`: Margin between the top edge of the `Histogram` and the maximum height bar. Set in pixels. Default: `3` */\n  barTopMargin?: number;\n  /** `dataStep`: Option to generate bars of a specified width in the X axis units. Overrides `barCount`. Default: `undefined` */\n  dataStep?: number;\n  /** `allowSelection`: Determines whether or not the `Histogram` allows users to select bars using a selection brush control. Default: `true` */\n  allowSelection?: boolean;\n  /** `stickySelection`: Stick selection brush coodrinates to the bar edges. Default: `true` */\n  stickySelection?: boolean;\n  /** `labelSideMargin`: Adjust the margin between the axis tick edge labels and the horizontal edges of the `Histogram` component bounding box. Default: `3` */\n  labelSideMargin?: number;\n  /** `formatter`: Function to format the axis tick edge labels in the Histogram component. */\n  formatter?: (n: number) => string;\n  /** `events`: Events for the `Histogram` component. */\n  events?: HistogramEvents;\n}\n\nexport interface HistogramEvents {\n  /**  `onBrush`: Callback for the range selection. Provides current selection of `Histogram`. */\n  onBrush?: (selection: [number, number] | undefined, isManuallySelected?: boolean) => void;\n  /**  `onBarHover`: Callback that is called when a bar is hovered over. Provides `BarData` for hovered bar: `rangeStart`, `rangeEnd` and `count` of records in this bar. */\n  onBarHover?: (data: BarData) => void;\n}\n\n"], "names": ["DEFAULT_PADDING", "top", "left", "bottom", "right", "HistogramConfig", "Config", "constructor", "this", "padding", "barsCount", "barPadding", "minBarHeight", "selectionRadius", "selectionPadding", "barCount", "dataStep", "undefined", "barRadius", "bar<PERSON>op<PERSON>argin", "labelSideMargin", "formatter", "allowSelection", "stickySelection", "events", "onBrush", "onBarHover"], "mappings": "wCAGa,MAAAA,EAA2B,CACtCC,IAAK,EACLC,KAAM,EACNC,OAAQ,EACRC,MAAO,GAGH,MAAOC,UAAwBC,EAArC,WAAAC,uBACEC,KAAOC,QAAGT,EACVQ,KAASE,UAAG,GACZF,KAAUG,WAAG,GACbH,KAAYI,aAAG,EACfJ,KAAeK,gBAAG,EAClBL,KAAgBM,iBAAG,EACnBN,KAAQO,SAAG,GACXP,KAAQQ,cAAGC,EACXT,KAASU,UAAG,EACZV,KAAYW,aAAG,EACfX,KAAeY,gBAAG,EAClBZ,KAASa,eAAGJ,EACZT,KAAcc,gBAAG,EACjBd,KAAee,iBAAG,EAClBf,KAAAgB,OAA0B,CACxBC,aAASR,EACTS,gBAAYT,EAEf"}