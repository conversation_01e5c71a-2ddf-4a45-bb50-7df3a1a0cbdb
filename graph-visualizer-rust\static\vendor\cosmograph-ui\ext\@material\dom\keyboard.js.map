{"version": 3, "file": "keyboard.js", "sources": ["../../../../../../node_modules/@material/dom/keyboard.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n/**\n * <PERSON><PERSON><PERSON> provides normalized string values for keys.\n */\nexport var KEY = {\n    UNKNOWN: 'Unknown',\n    BACKSPACE: 'Backspace',\n    ENTER: 'Enter',\n    SPACEBAR: 'Spacebar',\n    PAGE_UP: 'PageUp',\n    PAGE_DOWN: 'PageDown',\n    END: 'End',\n    HOME: 'Home',\n    ARROW_LEFT: 'ArrowLeft',\n    ARROW_UP: 'ArrowUp',\n    ARROW_RIGHT: 'ArrowRight',\n    ARROW_DOWN: 'ArrowDown',\n    DELETE: 'Delete',\n    ESCAPE: 'Escape',\n    TAB: 'Tab',\n};\nvar normalizedKeys = new Set();\n// IE11 has no support for new Map with iterable so we need to initialize this\n// by hand.\nnormalizedKeys.add(KEY.BACKSPACE);\nnormalizedKeys.add(KEY.ENTER);\nnormalizedKeys.add(KEY.SPACEBAR);\nnormalizedKeys.add(KEY.PAGE_UP);\nnormalizedKeys.add(KEY.PAGE_DOWN);\nnormalizedKeys.add(KEY.END);\nnormalizedKeys.add(KEY.HOME);\nnormalizedKeys.add(KEY.ARROW_LEFT);\nnormalizedKeys.add(KEY.ARROW_UP);\nnormalizedKeys.add(KEY.ARROW_RIGHT);\nnormalizedKeys.add(KEY.ARROW_DOWN);\nnormalizedKeys.add(KEY.DELETE);\nnormalizedKeys.add(KEY.ESCAPE);\nnormalizedKeys.add(KEY.TAB);\nvar KEY_CODE = {\n    BACKSPACE: 8,\n    ENTER: 13,\n    SPACEBAR: 32,\n    PAGE_UP: 33,\n    PAGE_DOWN: 34,\n    END: 35,\n    HOME: 36,\n    ARROW_LEFT: 37,\n    ARROW_UP: 38,\n    ARROW_RIGHT: 39,\n    ARROW_DOWN: 40,\n    DELETE: 46,\n    ESCAPE: 27,\n    TAB: 9,\n};\nvar mappedKeyCodes = new Map();\n// IE11 has no support for new Map with iterable so we need to initialize this\n// by hand.\nmappedKeyCodes.set(KEY_CODE.BACKSPACE, KEY.BACKSPACE);\nmappedKeyCodes.set(KEY_CODE.ENTER, KEY.ENTER);\nmappedKeyCodes.set(KEY_CODE.SPACEBAR, KEY.SPACEBAR);\nmappedKeyCodes.set(KEY_CODE.PAGE_UP, KEY.PAGE_UP);\nmappedKeyCodes.set(KEY_CODE.PAGE_DOWN, KEY.PAGE_DOWN);\nmappedKeyCodes.set(KEY_CODE.END, KEY.END);\nmappedKeyCodes.set(KEY_CODE.HOME, KEY.HOME);\nmappedKeyCodes.set(KEY_CODE.ARROW_LEFT, KEY.ARROW_LEFT);\nmappedKeyCodes.set(KEY_CODE.ARROW_UP, KEY.ARROW_UP);\nmappedKeyCodes.set(KEY_CODE.ARROW_RIGHT, KEY.ARROW_RIGHT);\nmappedKeyCodes.set(KEY_CODE.ARROW_DOWN, KEY.ARROW_DOWN);\nmappedKeyCodes.set(KEY_CODE.DELETE, KEY.DELETE);\nmappedKeyCodes.set(KEY_CODE.ESCAPE, KEY.ESCAPE);\nmappedKeyCodes.set(KEY_CODE.TAB, KEY.TAB);\nvar navigationKeys = new Set();\n// IE11 has no support for new Set with iterable so we need to initialize this\n// by hand.\nnavigationKeys.add(KEY.PAGE_UP);\nnavigationKeys.add(KEY.PAGE_DOWN);\nnavigationKeys.add(KEY.END);\nnavigationKeys.add(KEY.HOME);\nnavigationKeys.add(KEY.ARROW_LEFT);\nnavigationKeys.add(KEY.ARROW_UP);\nnavigationKeys.add(KEY.ARROW_RIGHT);\nnavigationKeys.add(KEY.ARROW_DOWN);\n/**\n * normalizeKey returns the normalized string for a navigational action.\n */\nexport function normalizeKey(evt) {\n    var key = evt.key;\n    // If the event already has a normalized key, return it\n    if (normalizedKeys.has(key)) {\n        return key;\n    }\n    // tslint:disable-next-line:deprecation\n    var mappedKey = mappedKeyCodes.get(evt.keyCode);\n    if (mappedKey) {\n        return mappedKey;\n    }\n    return KEY.UNKNOWN;\n}\n/**\n * isNavigationEvent returns whether the event is a navigation event\n */\nexport function isNavigationEvent(evt) {\n    return navigationKeys.has(normalizeKey(evt));\n}\n//# sourceMappingURL=keyboard.js.map"], "names": ["KEY", "UNKNOWN", "BACKSPACE", "ENTER", "SPACEBAR", "PAGE_UP", "PAGE_DOWN", "END", "HOME", "ARROW_LEFT", "ARROW_UP", "ARROW_RIGHT", "ARROW_DOWN", "DELETE", "ESCAPE", "TAB", "normalizedKeys", "Set", "add", "KEY_CODE", "mappedKeyCodes", "Map", "set", "navigationKeys", "normalizeKey", "evt", "key", "has", "<PERSON><PERSON><PERSON>", "get", "keyCode"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAyBU,IAACA,EAAM,CACbC,QAAS,UACTC,UAAW,YACXC,MAAO,QACPC,SAAU,WACVC,QAAS,SACTC,UAAW,WACXC,IAAK,MACLC,KAAM,OACNC,WAAY,YACZC,SAAU,UACVC,YAAa,aACbC,WAAY,YACZC,OAAQ,SACRC,OAAQ,SACRC,IAAK,OAELC,EAAiB,IAAIC,IAGzBD,EAAeE,IAAIlB,EAAIE,WACvBc,EAAeE,IAAIlB,EAAIG,OACvBa,EAAeE,IAAIlB,EAAII,UACvBY,EAAeE,IAAIlB,EAAIK,SACvBW,EAAeE,IAAIlB,EAAIM,WACvBU,EAAeE,IAAIlB,EAAIO,KACvBS,EAAeE,IAAIlB,EAAIQ,MACvBQ,EAAeE,IAAIlB,EAAIS,YACvBO,EAAeE,IAAIlB,EAAIU,UACvBM,EAAeE,IAAIlB,EAAIW,aACvBK,EAAeE,IAAIlB,EAAIY,YACvBI,EAAeE,IAAIlB,EAAIa,QACvBG,EAAeE,IAAIlB,EAAIc,QACvBE,EAAeE,IAAIlB,EAAIe,KACvB,IAAII,EACW,EADXA,EAEO,GAFPA,EAGU,GAHVA,EAIS,GAJTA,EAKW,GALXA,EAMK,GANLA,EAOM,GAPNA,EAQY,GARZA,EASU,GATVA,EAUa,GAVbA,EAWY,GAXZA,EAYQ,GAZRA,EAaQ,GAbRA,EAcK,EAELC,EAAiB,IAAIC,IAGzBD,EAAeE,IAAIH,EAAoBnB,EAAIE,WAC3CkB,EAAeE,IAAIH,EAAgBnB,EAAIG,OACvCiB,EAAeE,IAAIH,EAAmBnB,EAAII,UAC1CgB,EAAeE,IAAIH,EAAkBnB,EAAIK,SACzCe,EAAeE,IAAIH,EAAoBnB,EAAIM,WAC3Cc,EAAeE,IAAIH,EAAcnB,EAAIO,KACrCa,EAAeE,IAAIH,EAAenB,EAAIQ,MACtCY,EAAeE,IAAIH,EAAqBnB,EAAIS,YAC5CW,EAAeE,IAAIH,EAAmBnB,EAAIU,UAC1CU,EAAeE,IAAIH,EAAsBnB,EAAIW,aAC7CS,EAAeE,IAAIH,EAAqBnB,EAAIY,YAC5CQ,EAAeE,IAAIH,EAAiBnB,EAAIa,QACxCO,EAAeE,IAAIH,EAAiBnB,EAAIc,QACxCM,EAAeE,IAAIH,EAAcnB,EAAIe,KACrC,IAAIQ,EAAiB,IAAIN,IAclB,SAASO,EAAaC,GACzB,IAAIC,EAAMD,EAAIC,IAEd,GAAIV,EAAeW,IAAID,GACnB,OAAOA,EAGX,IAAIE,EAAYR,EAAeS,IAAIJ,EAAIK,SACvC,OAAIF,GAGG5B,EAAIC,OACf,CAvBAsB,EAAeL,IAAIlB,EAAIK,SACvBkB,EAAeL,IAAIlB,EAAIM,WACvBiB,EAAeL,IAAIlB,EAAIO,KACvBgB,EAAeL,IAAIlB,EAAIQ,MACvBe,EAAeL,IAAIlB,EAAIS,YACvBc,EAAeL,IAAIlB,EAAIU,UACvBa,EAAeL,IAAIlB,EAAIW,aACvBY,EAAeL,IAAIlB,EAAIY"}