services:
  graph:
    image: ghcr.io/oculairmedia/graphiti-api:main
    ports:
      - 8003:8000
    healthcheck:
      test:
        - CMD
        - python
        - -c
        - import urllib.request;
          urllib.request.urlopen('http://localhost:8000/healthcheck')
      interval: 10s
      timeout: 5s
      retries: 3
    depends_on:
      falkordb:
        condition: service_healthy
      graphiti-centrality-rs:
        condition: service_healthy
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY:-sk-dummy}
      - FALKORDB_HOST=falkordb
      - FALKORDB_PORT=6379
      - FALKORDB_URI=redis://falkordb:6379
      - USE_FALKORDB=true
      - PORT=8000
      - USE_OLLAMA=${USE_OLLAMA:-true}
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL:-http://*************:11434/v1}
      - OLLAMA_MODEL=${OLLAMA_MODEL:-gemma3:12b}
      - OLLAMA_EMBEDDING_MODEL=${OLLAMA_EMBEDDING_MODEL:-mxbai-embed-large:latest}
      - USE_OLLAMA_EMBEDDINGS=${USE_OLLAMA_EMBEDDINGS:-true}
      - USE_RUST_CENTRALITY=${USE_RUST_CENTRALITY:-true}
      - RUST_CENTRALITY_URL=${RUST_CENTRALITY_URL:-http://graphiti-centrality-rs:3003}
      - RUST_SERVER_URL=http://graph-visualizer-rust:3000
      - ENABLE_CACHE_INVALIDATION=true
      - GRAPHITI_DATA_WEBHOOK_URLS=${GRAPHITI_DATA_WEBHOOK_URLS:-http://graph-visualizer-rust:3000/api/webhooks/data-ingestion}
    networks:
      - graphiti_network
    labels:
      - homepage.group=Knowledge Management
      - homepage.name=Graphiti API
      - homepage.icon=si-graphql
      - homepage.href=http://*************:8000
      - homepage.description=Knowledge graph API service
  falkordb:
    image: falkordb/falkordb:latest
    command: >
      redis-server
      --loadmodule /var/lib/falkordb/bin/falkordb.so
      --protected-mode no
      --bind 0.0.0.0
      --save 60 1
      --dir /data
      --dbfilename falkordb.rdb
      --loglevel warning
      --maxmemory 2gb
      --maxmemory-policy allkeys-lru
    mem_limit: 3g
    mem_reservation: 1g
    healthcheck:
      test:
        - CMD
        - redis-cli
        - ping
      interval: 1s
      timeout: 3s
      retries: 10
      start_period: 3s
    ports:
      - 6389:6379 # Redis/FalkorDB port (changed from 6379)
      - 3100:3000 # FalkorDB UI
    volumes:
      - falkordb_data:/data
    environment:
      - REDIS_URL=redis://*************:6389
      - NEXT_PUBLIC_REDIS_URL=redis://*************:6389
      - NEXT_PUBLIC_API_URL=http://*************:6389
    networks:
      - graphiti_network
    labels:
      - homepage.group=Knowledge Management
      - homepage.name=FalkorDB
      - homepage.icon=si-redis
      - homepage.href=http://*************:3100
      - homepage.description=FalkorDB graph database
  graph-visualizer-rust:
    image: ghcr.io/oculairmedia/graphiti-rust-visualizer:main
    container_name: graphiti-visualizer-rust
    restart: unless-stopped
    ports:
      - 3000:3000
    environment:
      - FALKORDB_HOST=falkordb
      - FALKORDB_PORT=6379
      - GRAPH_NAME=graphiti_migration
      - RUST_LOG=graph_visualizer=debug,tower_http=debug
      - NODE_LIMIT=${NODE_LIMIT:-100000}
      - EDGE_LIMIT=${EDGE_LIMIT:-100000}
      - MIN_DEGREE_CENTRALITY=${MIN_DEGREE_CENTRALITY:-0.0}
      - CACHE_ENABLED=${CACHE_ENABLED:-true}
      - CACHE_TTL_SECONDS=${CACHE_TTL_SECONDS:-30}
      - CACHE_STRATEGY=${CACHE_STRATEGY:-aggressive}
      - FORCE_FRESH_DATA=${FORCE_FRESH_DATA:-false}
    depends_on:
      falkordb:
        condition: service_healthy
    networks:
      - graphiti_network
    labels:
      - homepage.group=Knowledge Management
      - homepage.name=Graph Visualizer (Rust)
      - homepage.icon=si-rust
      - homepage.href=http://*************:3000
      - homepage.description=High-performance Rust-based graph visualization
  frontend:
    image: ghcr.io/oculairmedia/graphiti-frontend:main
    container_name: graphiti-frontend-app
    restart: unless-stopped
    expose:
      - 80
    environment:
      - NODE_ENV=production
      - VITE_WS_URL=ws://*************:8088/rust-ws
      - VITE_API_BASE_URL=http://*************:8088/api
      - VITE_GRAPHITI_WS_URL=ws://*************:8088/ws
    depends_on:
      - graph-visualizer-rust
    networks:
      - graphiti_network
    labels:
      - homepage.group=Knowledge Management
      - homepage.name=Graphiti Frontend App
      - homepage.icon=si-react
      - homepage.description=React frontend application
  nginx:
    image: nginx:alpine
    container_name: graphiti-nginx
    restart: unless-stopped
    ports:
      - 8088:80
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - frontend
      - graph-visualizer-rust
    networks:
      - graphiti_network
    labels:
      - homepage.group=Knowledge Management
      - homepage.name=Graphiti Nginx Proxy
      - homepage.icon=si-nginx
      - homepage.href=http://*************:8088
      - homepage.description=Reverse proxy for Graphiti services
  # graphiti-mcp:
  #   build:
  #     context: ./mcp_server
  #     dockerfile: Dockerfile
  #   container_name: graphiti-mcp
  #   restart: unless-stopped
  #   ports:
  #     - "8001:8000" # MCP Server HTTP endpoint
  #   environment:
  #     - GRAPHITI_API_URL=http://graph:8000
  #     - NEO4J_URI=bolt://falkordb:6379
  #     - NEO4J_USER=default
  #     - NEO4J_PASSWORD=
  #     - OPENAI_API_KEY=${OPENAI_API_KEY:-}
  #     - MODEL_NAME=${MODEL_NAME:-qwen2.5:32b}
  #     - SMALL_MODEL_NAME=${SMALL_MODEL_NAME:-qwen2.5:7b}
  #     - OPENAI_BASE_URL=${OPENAI_BASE_URL:-http://host.docker.internal:11434/v1}
  #     - SEMAPHORE_LIMIT=${SEMAPHORE_LIMIT:-5}
  #   depends_on:
  #     graph:
  #       condition: service_healthy
  #   networks:
  #     - graphiti_network
  #   labels:
  #     - "homepage.group=AI Services"
  #     - "homepage.name=Graphiti MCP Server"
  #     - "homepage.icon=si-openai"
  #     - "homepage.href=http://*************:8001"
  #     - "homepage.description=Model Context Protocol server for AI assistants"
  #   command: ["uv", "run", "graphiti_mcp_server.py", "--transport", "sse"]

  graphiti-centrality-rs:
    build:
      context: ./graphiti-centrality-rs
      dockerfile: Dockerfile
    container_name: graphiti-centrality-rs
    restart: unless-stopped
    ports:
      - 3003:3003 # Rust centrality service
    environment:
      - FALKORDB_HOST=falkordb
      - FALKORDB_PORT=6379
      - GRAPH_NAME=graphiti_migration
      - BIND_ADDR=0.0.0.0:3003
      - RUST_LOG=graphiti_centrality=info,debug
    depends_on:
      falkordb:
        condition: service_healthy
    networks:
      - graphiti_network
    healthcheck:
      test:
        - CMD
        - curl
        - -f
        - http://localhost:3003/health
      interval: 10s
      timeout: 5s
      retries: 3
    labels:
      - homepage.group=Knowledge Management
      - homepage.name=Centrality Service (Rust)
      - homepage.icon=si-rust
      - homepage.href=http://*************:3003
      - homepage.description=High-performance centrality calculations with
        native FalkorDB algorithms
  # Automated backup service for FalkorDB
  falkordb-backup:
    # Use published image (uncomment after first GitHub Actions build):
    # image: ghcr.io/oculairmedia/graphiti-backup:latest
    # Or build locally (default):
    build:
      context: .
      dockerfile: Dockerfile.backup
    container_name: graphiti-falkordb-backup
    restart: unless-stopped
    ports:
      - 8091:8080  # Monitoring dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro  # Docker socket for container access
      - falkordb_backups:/backups/falkordb            # Backup storage volume
      - backup_status:/var/log                        # Status files for dashboard
    environment:
      - FALKORDB_CONTAINER_NAME=graphiti-falkordb-1
      - BACKUP_DIR=/backups/falkordb
      - BACKUP_RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-7}
      - BACKUP_RETENTION_WEEKLY=${BACKUP_RETENTION_WEEKLY:-4}
      - BACKUP_RETENTION_MONTHLY=${BACKUP_RETENTION_MONTHLY:-3}
      - BACKUP_WEBHOOK_URL=${BACKUP_WEBHOOK_URL:-}
      - RUN_INITIAL_BACKUP=${RUN_INITIAL_BACKUP:-true}
      - ENABLE_DASHBOARD=${ENABLE_DASHBOARD:-true}
      - STATUS_FILE=/var/log/backup_status.json
    depends_on:
      falkordb:
        condition: service_healthy
    networks:
      - graphiti_network
    healthcheck:
      test: ["CMD", "sh", "-c", "pgrep crond && curl -f http://localhost:8080/health || exit 1"]
      interval: 60s
      timeout: 10s
      start_period: 30s
      retries: 3
    labels:
      - homepage.group=Knowledge Management
      - homepage.name=FalkorDB Backup Service
      - homepage.icon=si-databricks
      - homepage.href=http://*************:8091
      - homepage.description=Automated backup service with monitoring dashboard

networks:
  graphiti_network:
    driver: bridge
volumes:
  falkordb_data: null
  falkordb_backups: null
  backup_status: null
