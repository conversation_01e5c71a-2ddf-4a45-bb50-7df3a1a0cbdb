import { Meta } from "@storybook/blocks";

<Meta title="Welcome to cosmos.gl" />

<p style={{ fontSize: '2rem', lineHeight: '1.25em' }}>Welcome to cosmos.gl — a high-performance WebGL library for visualizing network graphs and machine learning embeddings.</p>

<video style={{ width: '100%' }} src="https://user-images.githubusercontent.com/755708/173392407-9b05cbb6-d39e-4c2c-ab41-50900cfda823.mp4" loop autoPlay muted playsInline>
</video>

<p style={{ fontSize: '1.0rem' }}>Here you can find documentaion and examples of how to use cosmos.gl</p>

---

### Quick Start

Install the package:

```bash
npm install @cosmos.gl/graph
```


Get the data, [configure](../?path=/docs/configuration--docs) the graph and run the simulation:
```javascript
import { Graph } from '@cosmos.gl/graph'

const div = document.querySelector('div')
const config = {
  spaceSize: 4096,
  simulationFriction: 0.1, // keeps the graph inert
  simulationGravity: 0, // disables gravity
  simulationRepulsion: 0.5, // increases repulsion between points
  curvedLinks: true, // curved links
  fitViewDelay: 1000, // wait 1 second before fitting the view
  fitViewPadding: 0.3, // centers the graph width padding of ~30% of screen
  rescalePositions: true, // rescale positions
  enableDrag: true, // enable dragging points
  onClick: pointIndex => { console.log('Clicked point index: ', pointIndex) },
  /* ... */
}

const graph = new Graph(div, config)

// Points: [x1, y1, x2, y2, x3, y3]
const pointPositions = new Float32Array([
  0.0, 0.0,    // Point 1 at (0,0)
  1.0, 0.0,    // Point 2 at (1,0)
  0.5, 1.0,    // Point 3 at (0.5,1)
]);

graph.setPointPositions(pointPositions)

// Links: [sourceIndex1, targetIndex1, sourceIndex2, targetIndex2]
const links = new Float32Array([
  0, 1,    // Link from point 0 to point 1
  1, 2,    // Link from point 1 to point 2
  2, 0,    // Link from point 2 to point 0
]);

graph.setLinks(links)

graph.render()
```

Try the [Quick Start](../?path=/story/examples-beginners--quick-start) example.

### Examples
- [Basic Set-Up](../?path=/story/examples-beginners--basic-set-up)
- [Adding Point Labels](../?path=/story/examples-beginners--point-labels) (via [`@interacta/css-labels`](https://www.npmjs.com/package/@interacta/css-labels))


---
Copyright [OpenJS Foundation](https://openjsf.org) and cosmos.gl contributors. All rights reserved. The [OpenJS Foundation](https://openjsf.org) has registered trademarks and uses trademarks.  For a list of trademarks of the [OpenJS Foundation](https://openjsf.org), please see our [Trademark Policy](https://trademark-policy.openjsf.org/) and [Trademark List](https://trademark-list.openjsf.org/).  Trademarks and logos not indicated on the [list of OpenJS Foundation trademarks](https://trademark-list.openjsf.org) are trademarks™ or registered® trademarks of their respective holders. Use of them does not imply any affiliation with or endorsement by them.
[The OpenJS Foundation](https://openjsf.org/) | [Terms of Use](https://terms-of-use.openjsf.org/) | [Privacy Policy](https://privacy-policy.openjsf.org/) | [Bylaws](https://bylaws.openjsf.org/) | [Code of Conduct](https://code-of-conduct.openjsf.org) | [Trademark Policy](https://trademark-policy.openjsf.org/) | [Trademark List](https://trademark-list.openjsf.org/) | [Cookie Policy](https://www.linuxfoundation.org/cookies/)