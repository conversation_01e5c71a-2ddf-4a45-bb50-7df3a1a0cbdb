{"version": 3, "file": "constants.js", "sources": ["../../../../../../node_modules/@material/menu/constants.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\nvar cssClasses = {\n    MENU_SELECTED_LIST_ITEM: 'mdc-menu-item--selected',\n    MENU_SELECTION_GROUP: 'mdc-menu__selection-group',\n    ROOT: 'mdc-menu',\n};\nvar strings = {\n    ARIA_CHECKED_ATTR: 'aria-checked',\n    ARIA_DISABLED_ATTR: 'aria-disabled',\n    CHECKBOX_SELECTOR: 'input[type=\"checkbox\"]',\n    LIST_SELECTOR: '.mdc-list,.mdc-deprecated-list',\n    SELECTED_EVENT: 'MDCMenu:selected',\n    SKIP_RESTORE_FOCUS: 'data-menu-item-skip-restore-focus',\n};\nvar numbers = {\n    FOCUS_ROOT_INDEX: -1,\n};\nvar DefaultFocusState;\n(function (DefaultFocusState) {\n    DefaultFocusState[DefaultFocusState[\"NONE\"] = 0] = \"NONE\";\n    DefaultFocusState[DefaultFocusState[\"LIST_ROOT\"] = 1] = \"LIST_ROOT\";\n    DefaultFocusState[DefaultFocusState[\"FIRST_ITEM\"] = 2] = \"FIRST_ITEM\";\n    DefaultFocusState[DefaultFocusState[\"LAST_ITEM\"] = 3] = \"LAST_ITEM\";\n})(DefaultFocusState || (DefaultFocusState = {}));\nexport { cssClasses, strings, numbers, DefaultFocusState };\n//# sourceMappingURL=constants.js.map"], "names": ["DefaultFocusState", "cssClasses", "MENU_SELECTED_LIST_ITEM", "MENU_SELECTION_GROUP", "ROOT", "strings", "ARIA_CHECKED_ATTR", "ARIA_DISABLED_ATTR", "CHECKBOX_SELECTOR", "LIST_SELECTOR", "SELECTED_EVENT", "SKIP_RESTORE_FOCUS", "numbers", "FOCUS_ROOT_INDEX"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAsBG,IAgBCA,EAhBAC,EAAa,CACbC,wBAAyB,0BACzBC,qBAAsB,4BACtBC,KAAM,YAENC,EAAU,CACVC,kBAAmB,eACnBC,mBAAoB,gBACpBC,kBAAmB,yBACnBC,cAAe,iCACfC,eAAgB,mBAChBC,mBAAoB,qCAEpBC,EAAU,CACVC,kBAAmB,IAGvB,SAAWb,GACPA,EAAkBA,EAAwB,KAAI,GAAK,OACnDA,EAAkBA,EAA6B,UAAI,GAAK,YACxDA,EAAkBA,EAA8B,WAAI,GAAK,aACzDA,EAAkBA,EAA6B,UAAI,GAAK,WAC3D,CALD,CAKGA,IAAsBA,EAAoB,CAAA"}