/* Cosmograph dynamic label sizing */
.cosmograph-label-size-8 { font-size: 8px !important; }
.cosmograph-label-size-9 { font-size: 9px !important; }
.cosmograph-label-size-10 { font-size: 10px !important; }
.cosmograph-label-size-11 { font-size: 11px !important; }
.cosmograph-label-size-12 { font-size: 12px !important; }
.cosmograph-label-size-13 { font-size: 13px !important; }
.cosmograph-label-size-14 { font-size: 14px !important; }
.cosmograph-label-size-15 { font-size: 15px !important; }
.cosmograph-label-size-16 { font-size: 16px !important; }
.cosmograph-label-size-17 { font-size: 17px !important; }
.cosmograph-label-size-18 { font-size: 18px !important; }
.cosmograph-label-size-19 { font-size: 19px !important; }
.cosmograph-label-size-20 { font-size: 20px !important; }
.cosmograph-label-size-21 { font-size: 21px !important; }
.cosmograph-label-size-22 { font-size: 22px !important; }
.cosmograph-label-size-23 { font-size: 23px !important; }
.cosmograph-label-size-24 { font-size: 24px !important; }

/* Dynamic border width classes for text stroke */
.cosmograph-border-0 { 
  -webkit-text-stroke-width: 0px !important; 
  text-stroke-width: 0px !important; 
}
.cosmograph-border-0-5 { 
  -webkit-text-stroke-width: 0.5px !important; 
  text-stroke-width: 0.5px !important; 
  -webkit-text-stroke-color: rgba(0,0,0,0.3) !important; 
  text-stroke-color: rgba(0,0,0,0.3) !important; 
}
.cosmograph-border-1 { 
  -webkit-text-stroke-width: 1px !important; 
  text-stroke-width: 1px !important; 
  -webkit-text-stroke-color: rgba(0,0,0,0.4) !important; 
  text-stroke-color: rgba(0,0,0,0.4) !important; 
}
.cosmograph-border-1-5 { 
  -webkit-text-stroke-width: 1.5px !important; 
  text-stroke-width: 1.5px !important; 
  -webkit-text-stroke-color: rgba(0,0,0,0.5) !important; 
  text-stroke-color: rgba(0,0,0,0.5) !important; 
}
.cosmograph-border-2 { 
  -webkit-text-stroke-width: 2px !important; 
  text-stroke-width: 2px !important; 
  -webkit-text-stroke-color: rgba(0,0,0,0.6) !important; 
  text-stroke-color: rgba(0,0,0,0.6) !important; 
}
.cosmograph-border-2-5 { 
  -webkit-text-stroke-width: 2.5px !important; 
  text-stroke-width: 2.5px !important; 
  -webkit-text-stroke-color: rgba(0,0,0,0.7) !important; 
  text-stroke-color: rgba(0,0,0,0.7) !important; 
}
.cosmograph-border-3 { 
  -webkit-text-stroke-width: 3px !important; 
  text-stroke-width: 3px !important; 
  -webkit-text-stroke-color: rgba(0,0,0,0.8) !important; 
  text-stroke-color: rgba(0,0,0,0.8) !important; 
}
.cosmograph-border-3-5 { 
  -webkit-text-stroke-width: 3.5px !important; 
  text-stroke-width: 3.5px !important; 
  -webkit-text-stroke-color: rgba(0,0,0,0.8) !important; 
  text-stroke-color: rgba(0,0,0,0.8) !important; 
}
.cosmograph-border-4 { 
  -webkit-text-stroke-width: 4px !important; 
  text-stroke-width: 4px !important; 
  -webkit-text-stroke-color: rgba(0,0,0,0.9) !important; 
  text-stroke-color: rgba(0,0,0,0.9) !important; 
}
.cosmograph-border-4-5 { 
  -webkit-text-stroke-width: 4.5px !important; 
  text-stroke-width: 4.5px !important; 
  -webkit-text-stroke-color: rgba(0,0,0,0.9) !important; 
  text-stroke-color: rgba(0,0,0,0.9) !important; 
}
.cosmograph-border-5 { 
  -webkit-text-stroke-width: 5px !important; 
  text-stroke-width: 5px !important; 
  -webkit-text-stroke-color: rgba(0,0,0,1) !important; 
  text-stroke-color: rgba(0,0,0,1) !important; 
}