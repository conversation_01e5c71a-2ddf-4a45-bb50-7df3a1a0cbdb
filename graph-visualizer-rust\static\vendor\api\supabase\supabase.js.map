{"version": 3, "file": "supabase.js", "sources": ["../../../src/api/supabase/supabase.ts"], "sourcesContent": ["import { PostgrestError, createClient } from '@supabase/supabase-js'\n\n/* eslint-disable @typescript-eslint/naming-convention */\ntype Record = {\n  browser?: string;\n  hostname?: string;\n  is_library_metric?: boolean;\n  links_count?: number;\n  links_have_time?: boolean | null;\n  links_raw_columns?: number;\n  links_raw_lines?: number | null;\n  mode?: string | null;\n  nodes_count?: number;\n  nodes_have_time?: boolean | null;\n  nodes_raw_columns?: number;\n  nodes_raw_lines?: number | null;\n}\n/* eslint-disable @typescript-eslint/naming-convention */\n\n// Create a single supabase client for interacting with your database\nexport const supabase = createClient(\n  'https://xovkkfhojasbjinfslpx.supabase.co',\n  // eslint-disable-next-line max-len\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhvdmtrZmhvamFzYmppbmZzbHB4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE2OTM1ODQ0ODAsImV4cCI6MjAwOTE2MDQ4MH0.L3-X0p_un0oSTNubPwtfGo0D8g2bkPIfz7CaZ-iRYXY'\n)\n\nexport async function addMetrics (data: Record): Promise<PostgrestError | null> {\n  const { error } = await supabase\n    .from('metrics')\n    .insert(data)\n  return error\n}\n"], "names": ["supabase", "createClient", "async", "addMetrics", "data", "error", "from", "insert"], "mappings": "qDAoBa,MAAAA,EAAWC,EACtB,2CAEA,oNAGKC,eAAeC,EAAYC,GAChC,MAAMC,MAAEA,SAAgBL,EACrBM,KAAK,WACLC,OAAOH,GACV,OAAOC,CACT"}