{"version": 3, "file": "util.js", "sources": ["../../../../../../node_modules/@material/ripple/util.js"], "sourcesContent": ["/**\n * Stores result from supportsCssVariables to avoid redundant processing to\n * detect CSS custom variable support.\n */\nvar supportsCssVariables_;\nexport function supportsCssVariables(windowObj, forceRefresh) {\n    if (forceRefresh === void 0) { forceRefresh = false; }\n    var CSS = windowObj.CSS;\n    var supportsCssVars = supportsCssVariables_;\n    if (typeof supportsCssVariables_ === 'boolean' && !forceRefresh) {\n        return supportsCssVariables_;\n    }\n    var supportsFunctionPresent = CSS && typeof CSS.supports === 'function';\n    if (!supportsFunctionPresent) {\n        return false;\n    }\n    var explicitlySupportsCssVars = CSS.supports('--css-vars', 'yes');\n    // See: https://bugs.webkit.org/show_bug.cgi?id=154669\n    // See: README section on Safari\n    var weAreFeatureDetectingSafari10plus = (CSS.supports('(--css-vars: yes)') &&\n        CSS.supports('color', '#00000000'));\n    supportsCssVars =\n        explicitlySupportsCssVars || weAreFeatureDetectingSafari10plus;\n    if (!forceRefresh) {\n        supportsCssVariables_ = supportsCssVars;\n    }\n    return supportsCssVars;\n}\nexport function getNormalizedEventCoords(evt, pageOffset, clientRect) {\n    if (!evt) {\n        return { x: 0, y: 0 };\n    }\n    var x = pageOffset.x, y = pageOffset.y;\n    var documentX = x + clientRect.left;\n    var documentY = y + clientRect.top;\n    var normalizedX;\n    var normalizedY;\n    // Determine touch point relative to the ripple container.\n    if (evt.type === 'touchstart') {\n        var touchEvent = evt;\n        normalizedX = touchEvent.changedTouches[0].pageX - documentX;\n        normalizedY = touchEvent.changedTouches[0].pageY - documentY;\n    }\n    else {\n        var mouseEvent = evt;\n        normalizedX = mouseEvent.pageX - documentX;\n        normalizedY = mouseEvent.pageY - documentY;\n    }\n    return { x: normalizedX, y: normalizedY };\n}\n//# sourceMappingURL=util.js.map"], "names": ["supportsCssVariables_", "supportsCssVariables", "windowObj", "forceRefresh", "supportsCssVars", "CSS", "supports", "explicitlySupportsCssVars", "weAreFeatureDetectingSafari10plus", "getNormalizedEventCoords", "evt", "pageOffset", "clientRect", "x", "y", "normalizedX", "normalizedY", "documentX", "left", "documentY", "top", "type", "touchEvent", "changedTouches", "pageX", "pageY", "mouseEvent"], "mappings": "AAIA,IAAIA,EACG,SAASC,EAAqBC,EAAWC,QACvB,IAAjBA,IAA2BA,GAAe,GAC9C,IACIC,EADAC,EAAMH,EAAUG,IAEpB,GAAqC,kBAA1BL,IAAwCG,EAC/C,OAAOH,EAGX,KAD8BK,GAA+B,mBAAjBA,EAAIC,UAE5C,OAAO,EAEX,IAAIC,EAA4BF,EAAIC,SAAS,aAAc,OAGvDE,EAAqCH,EAAIC,SAAS,sBAClDD,EAAIC,SAAS,QAAS,aAM1B,OALAF,EACIG,GAA6BC,EAC5BL,IACDH,EAAwBI,GAErBA,CACX,CACO,SAASK,EAAyBC,EAAKC,EAAYC,GACtD,IAAKF,EACD,MAAO,CAAEG,EAAG,EAAGC,EAAG,GAEtB,IAGIC,EACAC,EAJAH,EAAIF,EAAWE,EAAGC,EAAIH,EAAWG,EACjCG,EAAYJ,EAAID,EAAWM,KAC3BC,EAAYL,EAAIF,EAAWQ,IAI/B,GAAiB,eAAbV,EAAIW,KAAuB,CAC3B,IAAIC,EAAaZ,EACjBK,EAAcO,EAAWC,eAAe,GAAGC,MAAQP,EACnDD,EAAcM,EAAWC,eAAe,GAAGE,MAAQN,CACtD,KACI,CACD,IAAIO,EAAahB,EACjBK,EAAcW,EAAWF,MAAQP,EACjCD,EAAcU,EAAWD,MAAQN,CACpC,CACD,MAAO,CAAEN,EAAGE,EAAaD,EAAGE,EAChC"}