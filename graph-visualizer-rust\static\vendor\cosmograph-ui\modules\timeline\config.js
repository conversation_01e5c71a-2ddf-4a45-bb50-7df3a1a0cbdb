import{defaultDateFormat as i}from"./format.js";import{Config as t}from"../../utils.js";const o={top:1,left:0,bottom:1,right:0};class s extends t{constructor(){super(...arguments),this.allowSelection=!0,this.showAnimationControls=!1,this.animationSpeed=50,this.padding=o,this.axisTickHeight=25,this.selectionRadius=3,this.selectionPadding=8,this.barCount=100,this.barRadius=1,this.barPadding=.1,this.barTopMargin=3,this.minBarHeight=1,this.dataStep=void 0,this.tickStep=void 0,this.formatter=i,this.events={onBrush:void 0,onBarHover:void 0,onAnimationPlay:void 0,onAnimationPause:void 0}}}export{o as DEFAULT_PADDING,s as TimelineConfig};
//# sourceMappingURL=config.js.map
