{"version": 3, "file": "format.js", "sources": ["../../../src/modules/timeline/format.ts"], "sourcesContent": ["import { timeFormat } from 'd3-time-format'\nimport { format as d3format } from 'd3-format'\nimport { timeSecond, timeMinute, timeHour, timeDay, timeMonth, timeWeek, timeYear } from 'd3-time'\n\nconst formatMillisecond = timeFormat('.%L')\nconst formatSecond = timeFormat(':%S')\nconst formatMinute = timeFormat('%I:%M')\nconst formatHour = timeFormat('%I %p')\nconst formatDay = timeFormat('%a %d')\nconst formatWeek = timeFormat('%b %d')\nconst formatMonth = timeFormat('%b')\nconst formatYear = timeFormat('%Y')\n\n\nexport const defaultDateFormat = (date: Date | number): string => {\n  const _date = new Date(date)\n  return (timeSecond(_date) < _date\n    ? formatMillisecond\n    : timeMinute(_date) < _date\n      ? formatSecond\n      : timeHour(_date) < _date\n        ? formatMinute\n        : timeDay(_date) < _date\n          ? formatHour\n          : timeMonth(_date) < _date\n            ? (timeWeek(_date) < _date ? formatDay : formatWeek)\n            : timeYear(_date) < _date\n              ? formatMonth\n              : formatYear)(_date)\n}\n\nexport const defaultNumFormat = (num: number): string => {\n  const f = d3format('.5~g')\n  return f(num)\n}\n"], "names": ["formatMillisecond", "timeFormat", "formatSecond", "formatMinute", "formatHour", "formatDay", "formatWeek", "formatMonth", "formatYear", "defaultDateFormat", "date", "_date", "Date", "timeSecond", "timeMinute", "timeHour", "timeDay", "timeMonth", "timeWeek", "timeYear"], "mappings": "yLAIA,MAAMA,EAAoBC,EAAW,OAC/BC,EAAeD,EAAW,OAC1BE,EAAeF,EAAW,SAC1BG,EAAaH,EAAW,SACxBI,EAAYJ,EAAW,SACvBK,EAAaL,EAAW,SACxBM,EAAcN,EAAW,MACzBO,EAAaP,EAAW,MAGjBQ,EAAqBC,IAChC,MAAMC,EAAQ,IAAIC,KAAKF,GACvB,OAAQG,EAAWF,GAASA,EACxBX,EACAc,EAAWH,GAASA,EAClBT,EACAa,EAASJ,GAASA,EAChBR,EACAa,EAAQL,GAASA,EACfP,EACAa,EAAUN,GAASA,EAChBO,EAASP,GAASA,EAAQN,EAAYC,EACvCa,EAASR,GAASA,EAChBJ,EACAC,GAAYG,EAAM"}