# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build
dist
build

# Development files
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
README.md
*.md

# Explicitly include src/lib directory
!src/
!src/**
!src/lib/
!src/lib/**

# Logs
logs
*.log

# Cache
.cache
.parcel-cache

# Coverage
coverage

# Temporary files
tmp
temp