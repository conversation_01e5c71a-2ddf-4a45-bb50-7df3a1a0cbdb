import{SvelteComponent as e,init as s,safe_not_equal as t,svg_element as a,attr as l,set_style as r,insert as c,append as i,noop as n,detach as o}from'./../../ext/svelte/internal/index.mjs.js';function d(e){let s,t;return{c(){s=a("svg"),t=a("path"),l(t,"fill","currentColor"),l(t,"d","M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"),l(s,"aria-hidden","true"),l(s,"focusable","false"),l(s,"data-prefix","fas"),l(s,"data-icon","search"),l(s,"class","svg-inline--fa fa-search fa-w-16"),l(s,"role","img"),l(s,"xmlns","http://www.w3.org/2000/svg"),r(s,"width","16px"),r(s,"height","16px"),l(s,"viewBox","0 0 512 512")},m(e,a){c(e,s,a),i(s,t)},p:n,i:n,o:n,d(e){e&&o(s)}}}class f extends e{constructor(e){super(),s(this,e,null,d,t,{})}}export{f as default};
//# sourceMappingURL=search-svg.svelte.js.map
