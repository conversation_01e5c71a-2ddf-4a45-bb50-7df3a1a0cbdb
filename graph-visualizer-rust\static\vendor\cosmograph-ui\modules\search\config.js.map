{"version": 3, "file": "config.js", "sources": ["../../../src/modules/search/config.ts"], "sourcesContent": ["import type { AccessorOption, SearchData } from './types'\n\nexport type SearchConfigInterface<T extends SearchData> = {\n  /** `isDisabled`: Makes `Search` component inactive.\n   *\n   * Default: `false` */\n  isDisabled?: boolean;\n  /** `minMatch`: The minimum characters needed to show suggestions.\n   *\n   * Default: `1` */\n  minMatch?: number;\n  /** `limitSuggestions`: The maximum number of suggestions shown. When the number of suggestions exceeds `limitSuggestions`, the rest of the suggestions will be omitteed. This can be tweaked to improve rendering performance is suggestion list is very long.\n   *\n   * If value is `undefined`, suggestions will not be limited.\n   *\n   * Default: `50` */\n  limitSuggestions?: number;\n  /** `truncateValues`: Maximum number of characters to be shown for each property of data object. When the number of characters exceeds `truncateValues`, the rest of the characters will be hidden. If value is `undefined`, full values will be shown.\n   *\n   * Default: `100` */\n  truncateValues?: number;\n  /** `maxVisibleItems`: The maximum items visible in the suggestions dropdown at once. When the number of suggestions exceeds `maxVisibleItems`, a scrollbar will be added to the dropdown list. `Search` will use height of first `maxVisibleItems` elements for suggestions dropdown.\n   *\n   * Default: `10` */\n  maxVisibleItems?: number;\n  /** `openListUpwards`: When set to `true` will open the dropdown list above the input field. If set to `false` the dropdown list will open below the input field.\n   *\n   * Default: `false` */\n  openListUpwards?: boolean;\n  /** `placeholder`: Specifies the placeholder text to be displayed in the search input field.\n   *\n   * Default: `Search` */\n  placeholder?: string;\n  /** `activeAccessorIndex`: Index of the currently active accessor function in the accessorOptions array. Used to programmatically set the selected accessor in the accessor dropdown.\n   *\n   * If `activeAccessorIndex` is set, the parent component should handle the `onAccessorSelect` callback to update when the selection changes.\n   *\n   * If `undefined`, controlling accessor selection will be performed be `Search` component.\n   *\n   * Default: `undefined`. */\n  activeAccessorIndex?: number;\n  /** `accessors`: An array of options that define how to access properties of the `SearchData<T>` for search input. The first item will be applied as default accessor to search. Can be switched via button that displays current active accessor. By default, first option of `accessors` array is used to process search input.\n   *\n   * Each option is an object with two properties:\n   * - `label`: A string that represents the human-readable name of the property. This is used for display purposes in the UI.\n   * - `accessor`: Function that retrieves a property of the `SearchData<T>` item that should be used for the search operation.\n   *\n   * Default: `[{ label: 'id', accessor: (n: SearchData) => n.id }]` */\n  accessors?: AccessorOption<T>[];\n  /** `ordering`: An object that specifies the order and inclusion of properties in the found data objects.\n   *\n   * - `order`: An array of strings defining the order of the properties in the search results. The strings should correspond to the properties of the search data or labels from `accessors` object.\n   * - `include`: An array of strings specifying which accessor labels or/and properties of the search data should be included in the search results. If not provided, all properties of `SearchData<T>` will be included.\n   *\n   * If `ordering` is not provided, all properties of the given data object will be displayed, in their original order.\n   *\n   * Default: `undefined` */\n  ordering?: {order?: string[]; include?: string[]};\n  /** `matchPalette`: Colors used to differentiate search results. The colors should be specified in a format that is recognized by CSS.\n   *\n   * Default: `['#fbb4ae80', '#b3cde380', '#ccebc580', '#decbe480', '#fed9a680', '#ffffcc80', '#e5d8bd80', '#fddaec80']` */\n  matchPalette?: string[];\n  /** `events`: Callback functions for search events.\n   *\n   * - `onSelect`: Function that will be called when the user selects an item from the suggestions list. Provides selected item as argument.\n   * - `onSearch`: Function that will be called when the user inputs a search term. Provides an array of `SearchData<T>` items that match current search term as argument.\n   * - `onEnter`: Function that will be called when the user hits Enter key in a search input. Provides current text content of search input field as argument. */\n  events?: SearchEvents<T>;\n}\n\nexport const defaultSearchConfig: SearchConfigInterface<SearchData> = {\n  isDisabled: false,\n  minMatch: 1,\n  limitSuggestions: 50,\n  truncateValues: 100,\n  maxVisibleItems: 10,\n  openListUpwards: false,\n  placeholder: 'Search...',\n  activeAccessorIndex: undefined,\n  accessors: [{ label: 'id', accessor: (n: SearchData) => n.id }],\n  matchPalette: ['#fbb4ae80', '#b3cde380', '#ccebc580', '#decbe480', '#fed9a680', '#ffffcc80', '#e5d8bd80', '#fddaec80'],\n  ordering: undefined,\n  events: {\n    onSelect: undefined,\n    onSearch: undefined,\n    onEnter: undefined,\n    onAccessorSelect: undefined,\n  },\n}\n\nexport interface SearchEvents<T extends SearchData> {\n  /** `onSelect`: Function that will be called when the user selects an item from the suggestions list. Provides selected item as argument. */\n  onSelect?: (foundMatch: T) => void;\n  /** `onSearch`: Function that will be called when the user inputs a search term. Provides an array of `SearchData<T>` items that match current search term as argument. */\n  onSearch?: (foundMatches?: T[]) => void;\n  /** `onEnter`: Function that will be called when the user hits Enter key in a search input. Provides current text content of search input field and current accessor as arguments. */\n  onEnter?: (input: string, accessor?: AccessorOption<T>) => void;\n  /** `onAccessorSelect`: Function that will be called when the user selects an accessor from the dropdown list. Provides selected accessor as argument and its index in accessors list. */\n  onAccessorSelect?: (accessor: {accessor: AccessorOption<T>; index: number}) => void;\n}\n"], "names": ["defaultSearchConfig", "isDisabled", "minMatch", "limitSuggestions", "truncateValues", "maxVisibleItems", "openListUpwards", "placeholder", "activeAccessorIndex", "undefined", "accessors", "label", "accessor", "n", "id", "matchPalette", "ordering", "events", "onSelect", "onSearch", "onEnter", "onAccessorSelect"], "mappings": "AAsEa,MAAAA,EAAyD,CACpEC,YAAY,EACZC,SAAU,EACVC,iBAAkB,GAClBC,eAAgB,IAChBC,gBAAiB,GACjBC,iBAAiB,EACjBC,YAAa,YACbC,yBAAqBC,EACrBC,UAAW,CAAC,CAAEC,MAAO,KAAMC,SAAWC,GAAkBA,EAAEC,KAC1DC,aAAc,CAAC,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,YAAa,aAC1GC,cAAUP,EACVQ,OAAQ,CACNC,cAAUT,EACVU,cAAUV,EACVW,aAASX,EACTY,sBAAkBZ"}