{"name": "@cosmograph/cosmos", "version": "1.6.1", "description": "GPU-based force graph layout and rendering", "jsdelivr": "dist/index.min.js", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "CC-BY-NC-4.0", "repository": "git://github.com/cosmograph-org/cosmos.git", "type": "module", "scripts": {"build": "rm -rf dist; rollup -c", "watch": "rollup -c -w", "lint": "eslint --cache ./src --ext .ts --ignore-path .gitignore", "lint:staged": "npx lint-staged"}, "engines": {"node": ">=12.2.0", "npm": ">=7.0.0"}, "keywords": ["graph", "webgl", "force", "simulation", "visualization"], "homepage": "https://cosmograph.app", "author": "cosmograph-org", "devDependencies": {"@rollup/plugin-alias": "^3.1.9", "@rollup/plugin-commonjs": "^22.0.1", "@rollup/plugin-node-resolve": "^13.3.0", "@rollup/plugin-terser": "^0.4.4", "@types/d3-array": "^3.0.3", "@types/d3-color": "^3.1.0", "@types/d3-ease": "^3.0.0", "@types/d3-scale": "^4.0.2", "@types/d3-selection": "^3.0.2", "@types/d3-transition": "^3.0.1", "@types/d3-zoom": "^3.0.1", "@typescript-eslint/eslint-plugin": "^5.30.5", "@typescript-eslint/parser": "^5.30.5", "@zerollup/ts-transform-paths": "^1.7.18", "eslint": "^8.19.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-unicorn": "^43.0.1", "lint-staged": "^13.0.3", "pre-commit": "^1.2.2", "rollup": "^2.76.0", "rollup-plugin-glslify": "^1.3.0", "rollup-plugin-typescript2": "^0.32.1", "ttypescript": "^1.5.15", "typescript": "^4.7.4"}, "dependencies": {"d3-array": "^3.2.0", "d3-color": "^3.1.0", "d3-ease": "^3.0.1", "d3-scale": "^4.0.2", "d3-selection": "^3.0.0", "d3-transition": "^3.0.1", "d3-zoom": "^3.0.0", "gl-bench": "^1.0.42", "gl-matrix": "^3.4.3", "random": "^4.1.0", "regl": "^2.1.0"}, "pre-commit": "lint:staged", "lint-staged": {"*.{js,ts}": "eslint --quiet --cache --fix"}}