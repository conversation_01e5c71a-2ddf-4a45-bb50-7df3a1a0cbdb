{"version": 3, "file": "utils.js", "sources": ["../src/utils.ts"], "sourcesContent": ["export const isNumber = <T>(a: T): boolean => typeof a === 'number'\nexport const isFunction = <T>(a: T): boolean => typeof a === 'function'\nexport const isUndefined = <T>(a: T): boolean => a === undefined\nexport const isNil = <T>(a: T): boolean => a == null\nexport const isString = <T>(a: T): boolean => typeof a === 'string'\nexport const isArray = <T>(a: T): boolean => Array.isArray(a)\nexport const isObject = <T>(a: T): boolean => (a instanceof Object)\nexport const isAClassInstance = <T>(a: T): boolean => (a as object).constructor.name !== 'Function' && (a as object).constructor.name !== 'Object'\nexport const isPlainObject = <T>(a: T): boolean => isObject(a) && !isArray(a) && !isFunction(a) && !isAClassInstance(a)\n\nexport const cloneDeep = <T>(obj: T, stack: Map<any, any> = new Map()): T => {\n  if (typeof obj !== 'object' || obj === null) {\n    return obj\n  }\n\n  if (obj instanceof Date) {\n    return new Date(obj.getTime()) as unknown as T\n  }\n\n  if (obj instanceof Array) {\n    const clone: unknown[] = []\n    stack.set(obj, clone)\n    for (const item of obj) {\n      clone.push(stack.has(item) ? stack.get(item) : cloneDeep(item, stack))\n    }\n    return obj\n  }\n\n  // Class instances will be copied without cloning\n  if (isAClassInstance(obj)) {\n    const clone = obj\n    return clone\n  }\n\n  if (obj instanceof Object) {\n    const clone = {} as T\n    stack.set(obj, clone)\n    const objAsRecord = obj as Record<string | number, unknown>\n    Object.keys(obj)\n      .reduce((newObj: typeof objAsRecord, key: string | number): typeof objAsRecord => {\n        newObj[key] = stack.has(objAsRecord[key]) ? stack.get(objAsRecord[key]) : cloneDeep(objAsRecord[key], stack)\n        return newObj\n      }, clone as typeof objAsRecord)\n\n    return clone\n  }\n\n  return obj\n}\n\nexport const merge = <T, K>(obj1: T, obj2: K, visited: Map<any, any> = new Map()): T & K => {\n  type Rec = Record<string | number, unknown>\n  const newObj = (isAClassInstance(obj1 as Rec) ? obj1 : cloneDeep(obj1)) as T & K\n  if ((obj1 as unknown) === (obj2 as unknown)) return obj1 as T & K\n\n  // Taking care of recursive structures\n  if (visited.has(obj2)) return visited.get(obj2)\n  else visited.set(obj2, newObj)\n\n  Object.keys(obj2 as Rec).forEach(key => {\n    if (isPlainObject((obj1 as Rec)[key]) && isPlainObject((obj2 as Rec)[key])) {\n      (newObj as Rec)[key] = merge((obj1 as Rec)[key], (obj2 as Rec)[key], visited)\n    } else if (isAClassInstance(obj2 as Rec)) {\n      (newObj as Rec)[key] = obj2\n    } else {\n      (newObj as Rec)[key] = cloneDeep((obj2 as Rec)[key])\n    }\n  })\n\n  return newObj\n}\n\nexport const isBetween = (num: number, min: number, max: number): boolean => {\n  return num >= +min && +num <= +max\n}\n\nexport const getCountsInRange = (valuesMap: Map<number | Date, number>, range: [Date | number, Date | number]): number => {\n  const [min, max]: (number | Date)[] = range\n  const values = Array.from(valuesMap.keys())\n  let count = 0\n  values.forEach(value => {\n    if (isBetween(+value, +min, +max)) {\n      count += valuesMap.get(value) ?? 0\n    }\n  })\n  return count\n}\n\nexport const getInnerDimensions = (node: HTMLElement): { width: number; height: number } => {\n  const computedStyle = getComputedStyle(node)\n\n  let width = node.clientWidth\n  let height = node.clientHeight\n\n  height -= parseFloat(computedStyle.paddingTop) + parseFloat(computedStyle.paddingBottom)\n  width -= parseFloat(computedStyle.paddingLeft) + parseFloat(computedStyle.paddingRight)\n  return { height, width }\n}\n\nexport class Config {\n  init<T extends Record<string | number, any>> (config: T): this {\n    const thisInstance = this as Record<string | number, any>\n    Object.keys(config).forEach(key => {\n      if (isPlainObject(thisInstance[key])) thisInstance[key] = merge(thisInstance[key], config[key])\n      else thisInstance[key] = config[key]\n    })\n\n    return this\n  }\n}\n"], "names": ["isFunction", "a", "isArray", "Array", "isObject", "Object", "isAClassInstance", "constructor", "name", "isPlainObject", "cloneDeep", "obj", "stack", "Map", "Date", "getTime", "clone", "set", "item", "push", "has", "get", "objAsRecord", "keys", "reduce", "newObj", "key", "merge", "obj1", "obj2", "visited", "for<PERSON>ach", "isBetween", "num", "min", "max", "getCountsInRange", "valuesMap", "range", "values", "from", "count", "value", "_a", "getInnerDimensions", "node", "computedStyle", "getComputedStyle", "width", "clientWidth", "height", "clientHeight", "parseFloat", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "Config", "init", "config", "thisInstance", "this"], "mappings": "AACO,MAAMA,EAAiBC,GAA+B,mBAANA,EAI1CC,EAAcD,GAAkBE,MAAMD,QAAQD,GAC9CG,EAAeH,GAAmBA,aAAaI,OAC/CC,EAAuBL,GAAqD,aAAlCA,EAAaM,YAAYC,MAA0D,WAAlCP,EAAaM,YAAYC,KACpHC,EAAoBR,GAAkBG,EAASH,KAAOC,EAAQD,KAAOD,EAAWC,KAAOK,EAAiBL,GAExGS,EAAY,CAAIC,EAAQC,EAAuB,IAAIC,OAC9D,GAAmB,iBAARF,GAA4B,OAARA,EAC7B,OAAOA,EAGT,GAAIA,aAAeG,KACjB,OAAO,IAAIA,KAAKH,EAAII,WAGtB,GAAIJ,aAAeR,MAAO,CACxB,MAAMa,EAAmB,GACzBJ,EAAMK,IAAIN,EAAKK,GACf,IAAK,MAAME,KAAQP,EACjBK,EAAMG,KAAKP,EAAMQ,IAAIF,GAAQN,EAAMS,IAAIH,GAAQR,EAAUQ,EAAMN,IAEjE,OAAOD,CACR,CAGD,GAAIL,EAAiBK,GAAM,CAEzB,OADcA,CAEf,CAED,GAAIA,aAAeN,OAAQ,CACzB,MAAMW,EAAQ,CAAA,EACdJ,EAAMK,IAAIN,EAAKK,GACf,MAAMM,EAAcX,EAOpB,OANAN,OAAOkB,KAAKZ,GACTa,QAAO,CAACC,EAA4BC,KACnCD,EAAOC,GAAOd,EAAMQ,IAAIE,EAAYI,IAAQd,EAAMS,IAAIC,EAAYI,IAAQhB,EAAUY,EAAYI,GAAMd,GAC/Fa,IACNT,GAEEA,CACR,CAED,OAAOL,CAAG,EAGCgB,EAAQ,CAAOC,EAASC,EAASC,EAAyB,IAAIjB,OAEzE,MAAMY,EAAUnB,EAAiBsB,GAAeA,EAAOlB,EAAUkB,GACjE,OAAKA,IAAsBC,EAAyBD,EAGhDE,EAAQV,IAAIS,GAAcC,EAAQT,IAAIQ,IACrCC,EAAQb,IAAIY,EAAMJ,GAEvBpB,OAAOkB,KAAKM,GAAaE,SAAQL,IAC3BjB,EAAemB,EAAaF,KAASjB,EAAeoB,EAAaH,IAClED,EAAeC,GAAOC,EAAOC,EAAaF,GAAOG,EAAaH,GAAMI,GAC5DxB,EAAiBuB,GACzBJ,EAAeC,GAAOG,EAEtBJ,EAAeC,GAAOhB,EAAWmB,EAAaH,GAChD,IAGID,EAAM,EAGFO,EAAY,CAACC,EAAaC,EAAaC,IAC3CF,IAAQC,IAAQD,IAAQE,EAGpBC,EAAmB,CAACC,EAAuCC,KACtE,MAAOJ,EAAKC,GAA0BG,EAChCC,EAASpC,MAAMqC,KAAKH,EAAUd,QACpC,IAAIkB,EAAQ,EAMZ,OALAF,EAAOR,SAAQW,UACTV,GAAWU,GAAQR,GAAMC,KAC3BM,GAAiC,QAAxBE,EAAAN,EAAUhB,IAAIqB,UAAU,IAAAC,EAAAA,EAAA,EAClC,IAEIF,CAAK,EAGDG,EAAsBC,IACjC,MAAMC,EAAgBC,iBAAiBF,GAEvC,IAAIG,EAAQH,EAAKI,YACbC,EAASL,EAAKM,aAIlB,OAFAD,GAAUE,WAAWN,EAAcO,YAAcD,WAAWN,EAAcQ,eAC1EN,GAASI,WAAWN,EAAcS,aAAeH,WAAWN,EAAcU,cACnE,CAAEN,SAAQF,QAAO,QAGbS,EACX,IAAAC,CAA8CC,GAC5C,MAAMC,EAAeC,KAMrB,OALAxD,OAAOkB,KAAKoC,GAAQ5B,SAAQL,IACtBjB,EAAcmD,EAAalC,IAAOkC,EAAalC,GAAOC,EAAMiC,EAAalC,GAAMiC,EAAOjC,IACrFkC,EAAalC,GAAOiC,EAAOjC,EAAI,IAG/BmC,IACR"}