import"d3-transition";import{ResizeObserver as i}from"@juggle/resize-observer";import{axisBottom as t}from"d3-axis";import{brushX as s}from"d3-brush";import{scaleSymlog as n,scaleTime as e,scaleLinear as a}from"d3-scale";import{select as o}from"d3-selection";import{extent as r,rollup as h,range as l,pairs as c}from"d3-array";import{play as _,pause as d}from"./assets.js";import{TimelineConfig as u}from"./config.js";export{TimelineConfig}from"./config.js";import v from"./style.module.css.js";import{getCountsInRange as m,getInnerDimensions as g}from"../../utils.js";class p{constructor(s,r){var h;if(this._isAnimationRunning=!1,this._svgParser=new DOMParser,this._height=0,this._width=0,this._timelineWidth=0,this._timelineHeight=0,this._config=new u,this._barWidth=0,this._maxCount=0,this._barsData=[],this._timeData=void 0,this._bandIntervals=[],this._isNumericTimeline=!1,this._firstRender=!0,this._yScale=n(),this._timeScale=e(),this._numScale=a(),this._activeAxisScale=this._timeScale,this._timeAxis=t(this._timeScale),this._numAxis=t(this._numScale),this._toggleAnimation=i=>{i.preventDefault(),this._isAnimationRunning?this.pauseAnimation():this.playAnimation()},this._disableAnimation=()=>{var i,t;this.pauseAnimation(),null===(i=this._animationControlDiv)||void 0===i||i.removeEventListener("click",this._toggleAnimation),null===(t=this._animationControlDiv)||void 0===t||t.remove()},this.playAnimation=()=>{var i,t,s,n;clearInterval(this._animationInterval),this._currentSelectionInPixels&&(this._animationInterval=setInterval(this._animateSelection,this._config.animationSpeed),this._isAnimationRunning=!0,null===(t=(i=this._config.events).onAnimationPlay)||void 0===t||t.call(i,this._isAnimationRunning,this._currentSelection)),null===(s=this._pauseButtonSvg)||void 0===s||s.classList.remove(v.hidden),null===(n=this._playButtonSvg)||void 0===n||n.classList.add(v.hidden)},this.pauseAnimation=()=>{var i,t,s,n;clearInterval(this._animationInterval),this._isAnimationRunning=!1,null===(t=(i=this._config.events).onAnimationPause)||void 0===t||t.call(i,this._isAnimationRunning,this._currentSelection),null===(s=this._pauseButtonSvg)||void 0===s||s.classList.add(v.hidden),null===(n=this._playButtonSvg)||void 0===n||n.classList.remove(v.hidden)},this.stopAnimation=()=>{var i,t;this.pauseAnimation(),this.setSelection(void 0),null===(t=(i=this._config.events).onBrush)||void 0===t||t.call(i,void 0)},this._animateSelection=()=>{var i,t;const s=this._currentSelectionInPixels;s&&void 0!==s[0]&&void 0!==s[1]&&(this.setSelectionInPixels([s[0]+this._barWidth,s[1]+this._barWidth]),s[1]!==(null===(i=this._currentSelectionInPixels)||void 0===i?void 0:i[1])&&void 0!==(null===(t=this._currentSelectionInPixels)||void 0===t?void 0:t[1])||this.stopAnimation())},this._checkLastTickPosition=()=>{var i;const t=this._axisGroup.selectAll(".tick:last-of-type").nodes();if(null==t?void 0:t.length){const s=t[0],n=null==s?void 0:s.getBoundingClientRect().right,e=null===(i=this._svg)||void 0===i?void 0:i.getBoundingClientRect().right;s.style.display=n>=e?"none":"inherit"}},this.destroy=()=>{this._containerNode.innerHTML="",clearInterval(this._animationInterval)},r&&this._config.init(r),this._containerNode=s,this._svg=document.createElementNS("http://www.w3.org/2000/svg","svg"),this._svg.classList.add(v.timelineSvg),this._animationControlDiv=document.createElement("div"),this._animationControlDiv.classList.add(v.animationControl),this._containerNode.classList.add(v.timeline),this._containerNode.appendChild(this._svg),this._noDataDiv=document.createElement("div"),o(this._noDataDiv).style("display","none").attr("class",v.noData).append("div").text("No timeline data"),this._containerNode.appendChild(this._noDataDiv),null===(h=this._config)||void 0===h?void 0:h.showAnimationControls){const i=setInterval((()=>{null!==this._containerNode&&(this._initAnimationControls(),clearInterval(i))}),100)}this._barsGroup=o(this._svg).append("g").attr("class",v.bars),this._axisGroup=o(this._svg).append("g").attr("class",v.axis),this._brushGroup=o(this._svg).append("g").attr("class",v.brush),this._timeAxis.tickFormat(this._config.formatter),this._numAxis.tickFormat(this._config.formatter),this._resizeObserver=new i((i=>{window.requestAnimationFrame((()=>{Array.isArray(i)&&i.length&&this.resize()}))})),this._resizeObserver=new i((()=>{this.resize()})),this._resizeObserver.observe(this._containerNode)}get _barPadding(){return this._barWidth*this._config.barPadding}getCurrentSelection(){return this._currentSelection}getCurrentSelectionInPixels(){return this._currentSelectionInPixels}getBarWidth(){return this._barWidth-this._barPadding}getConfig(){return this._config}getIsAnimationRunning(){return this._isAnimationRunning}setConfig(i){var t,s,n,e,a,o;const r=JSON.parse(JSON.stringify(this._config));i?this._config.init(i):this._config=new u,(null===(t=this._config)||void 0===t?void 0:t.showAnimationControls)?(null===(s=this._animationControlDiv)||void 0===s?void 0:s.isConnected)||this._initAnimationControls():this._animationControlDiv&&this._disableAnimation(),this._config.allowSelection||this._disableBrush(),this._config.formatter&&(this._timeAxis.tickFormat(this._config.formatter),this._numAxis.tickFormat(this._config.formatter)),(null===(n=this._config)||void 0===n?void 0:n.dataStep)===(null===(e=r.config)||void 0===e?void 0:e.dataStep)&&(null===(a=this._config)||void 0===a?void 0:a.barCount)===(null===(o=r.config)||void 0===o?void 0:o.barCount)||this._updateTimelineData(),this.resize()}setTimeData(i){var t,s,n;this._timeData=null==i?void 0:i.filter((i=>!isNaN(+i)&&void 0!==i)),this._currentSelection=void 0,null===(s=(t=this._config.events).onBrush)||void 0===s||s.call(t,this._currentSelection),this._updateScales(),o(this._noDataDiv).style("display","none"),(null===(n=this._timeData)||void 0===n?void 0:n.length)?(this._dateExtent=r(this._timeData),this._updateTimelineData()):(this._barsData=[],this._axisGroup.selectAll("*").remove(),this._barsGroup.selectAll("*").remove(),this._brushGroup.selectAll("*").remove(),o(this._noDataDiv).style("display","block"),this._firstRender=!0)}_getBarsData(i,t){var s,n;if(!(i[1]<=i[0])&&(null===(s=this._timeData)||void 0===s?void 0:s.length)&&this._dateExtent){const s=h(this._timeData,(i=>i.length),(i=>i)),e=null!==(n=this._config.dataStep)&&void 0!==n?n:(i[1]-i[0])/(this._config.barCount-1);if(0===e)return;this._bandIntervals=l(+i[0],+i[1],e);const a=this._bandIntervals[this._bandIntervals.length-1];let o=this._config.dataStep?+a+e:i[1];t&&(this._bandIntervals=this._bandIntervals.map((i=>new Date(i))),o=new Date(o)),a<i[1]&&this._bandIntervals.push(o);const r=c(this._bandIntervals);this._barsData=r.map((i=>({rangeStart:i[0],rangeEnd:i[1],count:m(s,i)})))}}_updateTimelineData(){var i;if((null===(i=this._timeData)||void 0===i?void 0:i.length)&&this._dateExtent){if(this._isNumericTimeline=!(this._timeData[0]instanceof Date),this._isNumericTimeline)this._getBarsData(this._dateExtent);else{this._timeData=this._timeData.map((i=>new Date(i)));const i=this._dateExtent.map((i=>{var t;return null!==(t=i.getTime())&&void 0!==t?t:0}));this._getBarsData(i)}this._maxCount=Math.max(...this._barsData.map((i=>i.count)))}}setSelection(i,t=!1){var s,n,e,a;const o=this._currentSelection;i&&this._dateExtent&&i[0]>=this._dateExtent[0]&&i[1]<=this._dateExtent[1]&&i[0]<i[1]?(this._currentSelection=i,this._currentSelectionInPixels=this._currentSelection.map(this._activeAxisScale),null===(s=this._animationControlDiv)||void 0===s||s.classList.remove(v.disabled)):(this._currentSelection=void 0,this._currentSelectionInPixels=void 0,null===(n=this._animationControlDiv)||void 0===n||n.classList.add(v.disabled));const{_currentSelection:r}=this;t||(null==o?void 0:o[0])===(null==r?void 0:r[0])&&(null==o?void 0:o[1])===(null==r?void 0:r[1])||null===(a=(e=this._config.events).onBrush)||void 0===a||a.call(e,this._currentSelection,!0),this._brushInstance&&!this._firstRender&&this._brushGroup.call(this._brushInstance.move,this._currentSelectionInPixels)}setSelectionInPixels(i){var t,s,n,e,a;i&&i[0]>0&&i[1]<this._timelineWidth&&i[0]<i[1]?(this._currentSelection=i.map((i=>this._activeAxisScale.invert(i))),this._currentSelectionInPixels=null===(t=this._currentSelection)||void 0===t?void 0:t.map(this._activeAxisScale),null===(s=this._animationControlDiv)||void 0===s||s.classList.remove(v.disabled)):(this._currentSelection=void 0,this._currentSelectionInPixels=void 0,null===(n=this._animationControlDiv)||void 0===n||n.classList.add(v.disabled)),this._brushInstance&&!this._firstRender&&this._brushGroup.call(this._brushInstance.move,this._currentSelectionInPixels),null===(a=(e=this._config.events).onBrush)||void 0===a||a.call(e,this._currentSelection)}resize(){const{height:i,width:t}=g(this._containerNode),{offsetWidth:s}=this._animationControlDiv;this._width=t,this._height=i,this._timelineWidth=this._width-this._config.padding.left-this._config.padding.right-s,this._timelineHeight=this._height-this._config.padding.top-this._config.padding.bottom,this._timelineHeight>this._config.padding.top+this._config.padding.bottom&&(this._updateScales(),this._checkLastTickPosition(),this._currentSelection&&this.setSelection(this._currentSelection,!0),this.render())}render(){this._updateBrush(),this._updateBars(),this._updateAxis(),this._firstRender&&(this._firstRender=!1)}_updateAxis(){this._timeData&&(this._axisGroup.style("transform",`translate(${this._config.padding.left}px, ${this._config.padding.top+this._config.axisTickHeight+1+this._config.selectionPadding/2}px)`).call(this._isNumericTimeline?this._numAxis:this._timeAxis).call((i=>i.select(".domain").remove())),this._axisGroup.selectAll(".tick").select("text").attr("class",v.axisTick).attr("y",0).attr("dy",-this._config.axisTickHeight).attr("dx","5px"),this._axisGroup.selectAll("line").attr("class",v.axisLine).attr("y2",-this._config.axisTickHeight))}_updateBrush(){var i;this._config.allowSelection&&(this._brushGroup.style("transform",`translate(${this._config.padding.left}px, ${this._config.padding.top}px)`),this._brushInstance=s().extent([[0,0],[this._timelineWidth,this._timelineHeight]]),this._brushInstance.on("end",(({selection:i,sourceEvent:t})=>{var s,n,e,a,o,r,h;t&&(i?(this._currentSelection=i.map((i=>this._activeAxisScale.invert(i))),this._currentSelectionInPixels=null===(s=this._currentSelection)||void 0===s?void 0:s.map(this._activeAxisScale),null===(n=this._animationControlDiv)||void 0===n||n.classList.remove(v.disabled),null===(a=(e=this._config.events).onBrush)||void 0===a||a.call(e,this._currentSelection)):(this._currentSelection=void 0,this._currentSelectionInPixels=void 0,null===(r=(o=this._config.events).onBrush)||void 0===r||r.call(o,void 0),null===(h=this._animationControlDiv)||void 0===h||h.classList.add(v.disabled)))})),this._brushGroup.call(this._brushInstance),this._currentSelection?(this._currentSelectionInPixels=this._currentSelection.map(this._activeAxisScale),this._brushGroup.call(this._brushInstance.move,this._currentSelectionInPixels)):null===(i=this._brushInstance)||void 0===i||i.clear(this._brushGroup),this._brushGroup.select("rect.selection").classed(v.selection,!0).attr("rx",this._config.selectionRadius).attr("ry",this._config.selectionRadius))}_updateBars(){this._barsGroup.style("transform",`translate(${this._config.padding.left}px, ${this._config.padding.top-this._config.selectionPadding/2}px)`);const i=this._barsGroup.selectAll(`.${v.bar}`).data(this._barsData).join("rect").attr("class",v.bar).attr("x",(i=>this._activeAxisScale(+i.rangeStart)+this._barPadding/2)).attr("width",this.getBarWidth()).attr("rx",this._config.barRadius).attr("ry",this._config.barRadius).attr("y",-this._timelineHeight);this._config.events.onBarHover&&i.on("mouseover",this._config.events.onBarHover),i.transition().duration(300).attr("height",(i=>this._yScale(i.count))).style("opacity",(i=>this._yScale(i.count)===this._config.minBarHeight?.25:1))}_updateScales(){if(!this._dateExtent||!this._barsData.length)return;const i=this._barsData[this._barsData.length-1];if(this._config.tickStep){const i=l(+this._dateExtent[0],+this._dateExtent[1],this._config.tickStep);this._isNumericTimeline?this._numAxis.tickValues(i):this._timeAxis.tickValues(i.map((i=>new Date(i))))}this._yScale.range([this._config.minBarHeight,this._timelineHeight-this._config.barTopMargin-this._config.selectionPadding]).domain([0,this._maxCount]).clamp(!0),this._isNumericTimeline?(this._numScale.domain([this._dateExtent[0],i.rangeEnd]).range([0,this._timelineWidth]).clamp(!0),this._activeAxisScale=this._numScale):(this._timeScale.domain([this._dateExtent[0],i.rangeEnd]).range([0,this._timelineWidth]).clamp(!0),this._activeAxisScale=this._timeScale);const t=this._barsData[0],s=this._activeAxisScale(t.rangeEnd)-this._activeAxisScale(t.rangeStart);this._barWidth=s}_disableBrush(){var i,t;null===(i=this._brushInstance)||void 0===i||i.clear(this._brushGroup),this._currentSelectionInPixels=void 0,this._currentSelection=void 0,this.pauseAnimation(),this._brushGroup.selectAll("*").remove(),this._config.showAnimationControls&&(null===(t=this._animationControlDiv)||void 0===t||t.classList.add(v.disabled))}async _initAnimationControls(){this._containerNode.insertBefore(this._animationControlDiv,this._svg);await(async()=>{var i,t;if(!this._animationControlDiv.firstChild){const s=this._svgParser.parseFromString(_,"image/svg+xml").firstChild,n=this._svgParser.parseFromString(d,"image/svg+xml").firstChild;this._pauseButtonSvg=null===(i=this._animationControlDiv)||void 0===i?void 0:i.appendChild(n),this._playButtonSvg=null===(t=this._animationControlDiv)||void 0===t?void 0:t.appendChild(s)}})().then((()=>{var i,t,s,n,e;this._isAnimationRunning?(null===(s=this._playButtonSvg)||void 0===s||s.classList.add(v.playAnimation,v.hidden),null===(n=this._pauseButtonSvg)||void 0===n||n.classList.add(v.pauseAnimation)):(null===(i=this._playButtonSvg)||void 0===i||i.classList.add(v.playAnimation),null===(t=this._pauseButtonSvg)||void 0===t||t.classList.add(v.pauseAnimation,v.hidden)),this._currentSelection||null===(e=this._animationControlDiv)||void 0===e||e.classList.add(v.disabled),this._animationControlDiv.addEventListener("click",this._toggleAnimation)}))}}export{p as Timeline};
//# sourceMappingURL=index.js.map
