{"version": 3, "file": "config.js", "sources": ["../../../src/modules/timeline/config.ts"], "sourcesContent": ["import { defaultDateFormat } from './format'\nimport { Config } from '../../utils'\nimport type { BarData, Padding } from './types'\n\nexport const DEFAULT_PADDING: Padding = {\n  top: 1,\n  left: 0,\n  bottom: 1,\n  right: 0,\n}\n\nexport class TimelineConfig extends Config implements TimelineConfigInterface {\n  allowSelection = true\n  showAnimationControls = false\n  animationSpeed = 50\n  padding = DEFAULT_PADDING\n  axisTickHeight = 25\n  selectionRadius = 3\n  selectionPadding = 8\n  barCount = 100\n  barRadius = 1\n  barPadding = 0.1\n  barTopMargin = 3\n  minBarHeight = 1\n  dataStep = undefined\n  tickStep = undefined\n  formatter = defaultDateFormat\n  events: TimelineEvents = {\n    onBrush: undefined,\n    onBarHover: undefined,\n    onAnimationPlay: undefined,\n    onAnimationPause: undefined,\n  }\n}\nexport interface TimelineConfigInterface {\n  /** `padding`: Padding between the outer edges of the timeline. Affects only timeline container without animation button. Set in pixels. Default: `{ top: 0, bottom: 0, left: 0, right: 0 }` */\n  padding?: Padding;\n  /** `axisTickHeight`: Height of the ticks that appear along the timeline axis. Set in pixels. Default: `25` */\n  axisTickHeight?: number;\n  /** `selectionRadius`: Corners roundness of the data selection brush. Set in pixels. Default: `3` */\n  selectionRadius?: number;\n  /** `selectionPadding`: Padding of the data selection brush. Set in pixels. Default: `8` */\n  selectionPadding?: number;\n  /** `barCount`: Number of bars to be displayed in the timeline. Ignored if `dataStep` is set. Default: `100` */\n  barCount?: number;\n  /** `barRadius`: Corners roundness of each bar on the timeline. Set in pixels. Default: `1` */\n  barRadius?: number;\n  /** `barPadding`: Padding between each bar on the timeline. Set in percent of bar width from 0 (as 0% of the bar width) to 1 (as 100% of the bar width). Default: `0.1` */\n  barPadding?: number;\n  /** `barTopMargin`: Margin between the top edge of the timeline and the maximum height bar. Set in pixels. Default: `3` */\n  barTopMargin?: number;\n  /** `minBarHeight`: Height of bars with an empty data on the timeline. Set in pixels. Default: `1` */\n  minBarHeight?: number;\n  /** `allowSelection`: Determines whether or not the timeline allows users to select a range of dates using a selection brush control. Default: `true` */\n  allowSelection?: boolean;\n  /** `showAnimationControls`: If set to true, shows an animation control button that allows to play or pause animation of selected range of dates. Default: `false` */\n  showAnimationControls?: boolean;\n  /** `animationSpeed`: Rate of refresh for each selection brush movement. Set in ms. Default: `50` */\n  animationSpeed?: number;\n  /** `dataStep`: Generate bars of width of this value mapped in the X axis units. Overrides `barCount`. Set in ms for `Date[]` data. Default: `undefined` */\n  dataStep?: number;\n  /** `tickStep`: Interval between each tick mark on the timeline axis. Set in the X axis units, in `ms` for `Date[]` timeline data or in relative units for `number[]` timeline data. Custom `dateFormat` may be required for the proper display of tick labels if the timeline data is `Date[]`. Default: `undefined` */\n  tickStep?: number;\n  /** `formatter`: Formatter function for ticks displayed on the timeline axis. */\n  formatter?: (date: Date | number) => string;\n  /** `events`: Events for the `Timeline` component */\n  events?: TimelineEvents;\n}\n\nexport interface TimelineEvents {\n  /**  `onBrush`: Callback for the range selection. Provides current selection of `Timeline`. */\n  onBrush?: (selection: [Date, Date] | [number, number] | undefined, isManuallySelected?: boolean) => void;\n  /**  `onBarHover`: Callback that is called when a bar is hovered over. Provides `BarData` for hovered bar: `rangeStart`, `rangeEnd` and `count` of records in this bar. */\n  onBarHover?: (data: BarData) => void;\n  /**  `onAnimationPlay`: Callback for the animation play that will be executed in `playAnimation. Provides `isAnimationRunning` state and current selection of `Timeline`. */\n  onAnimationPlay?: (isAnimationRunning: boolean, selection: (number | Date)[] | undefined) => void;\n  /**  `onAnimationPause`: Callback for the animation play that will be executed in `pauseAnimation`. Provides `isAnimationRunning` state and current selection of `Timeline`. */\n  onAnimationPause?: (isAnimationRunning: boolean, selection: (number | Date)[] | undefined) => void;\n}\n"], "names": ["DEFAULT_PADDING", "top", "left", "bottom", "right", "TimelineConfig", "Config", "constructor", "this", "allowSelection", "showAnimationControls", "animationSpeed", "padding", "axisTickHeight", "selectionRadius", "selectionPadding", "barCount", "barRadius", "barPadding", "bar<PERSON>op<PERSON>argin", "minBarHeight", "dataStep", "undefined", "tickStep", "formatter", "defaultDateFormat", "events", "onBrush", "onBarHover", "onAnimationPlay", "onAnimationPause"], "mappings": "wFAIa,MAAAA,EAA2B,CACtCC,IAAK,EACLC,KAAM,EACNC,OAAQ,EACRC,MAAO,GAGH,MAAOC,UAAuBC,EAApC,WAAAC,uBACEC,KAAcC,gBAAG,EACjBD,KAAqBE,uBAAG,EACxBF,KAAcG,eAAG,GACjBH,KAAOI,QAAGZ,EACVQ,KAAcK,eAAG,GACjBL,KAAeM,gBAAG,EAClBN,KAAgBO,iBAAG,EACnBP,KAAQQ,SAAG,IACXR,KAASS,UAAG,EACZT,KAAUU,WAAG,GACbV,KAAYW,aAAG,EACfX,KAAYY,aAAG,EACfZ,KAAQa,cAAGC,EACXd,KAAQe,cAAGD,EACXd,KAASgB,UAAGC,EACZjB,KAAAkB,OAAyB,CACvBC,aAASL,EACTM,gBAAYN,EACZO,qBAAiBP,EACjBQ,sBAAkBR,EAErB"}