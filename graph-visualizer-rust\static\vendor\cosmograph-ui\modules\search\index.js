import{defaultSearchConfig as t}from"./config.js";import{Events as e}from"./types.js";import i from"./search.svelte.js";import{merge as s}from"../../utils.js";class o{constructor(o,n){this._config={},this._containerNode=o,this._config=s(t,null!=n?n:{}),this._search=new i({target:o,props:{config:this._config}}),this._search.$on(e.Input,(({detail:t})=>{var e,i;return null===(i=null===(e=this._config.events)||void 0===e?void 0:e.onSearch)||void 0===i?void 0:i.call(e,t)})),this._search.$on(e.Select,(({detail:t})=>{var e,i;return null===(i=null===(e=this._config.events)||void 0===e?void 0:e.onSelect)||void 0===i?void 0:i.call(e,t)})),this._search.$on(e.Enter,(({detail:t})=>{var e,i;return null===(i=null===(e=this._config.events)||void 0===e?void 0:e.onEnter)||void 0===i?void 0:i.call(e,t)})),this._search.$on(e.AccessorSelect,(({detail:t})=>{var e,i;return null===(i=null===(e=this._config.events)||void 0===e?void 0:e.onAccessorSelect)||void 0===i?void 0:i.call(e,t)}))}setData(t){this._search.$set({data:t,textInput:""})}setConfig(e){this._config=s(t,null!=e?e:{}),this._search.$set({config:this._config,textInput:""})}setListState(t){this._search.setListState(t)}clearInput(){this._search.$set({textInput:""})}getConfig(){return this._config}destroy(){this._containerNode.innerHTML=""}}export{o as Search};
//# sourceMappingURL=index.js.map
