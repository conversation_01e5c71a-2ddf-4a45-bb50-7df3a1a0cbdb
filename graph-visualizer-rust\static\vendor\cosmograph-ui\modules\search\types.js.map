{"version": 3, "file": "types.js", "sources": ["../../../src/modules/search/types.ts"], "sourcesContent": ["export enum Events {\n  Input = 'input',\n  Select = 'select',\n  Enter = 'enter',\n  AccessorSelect = 'accessorSelect',\n}\n\nexport type SearchData = { id: string; [key: string]: any }\nexport type AccessorOption<T extends SearchData> = { label: string; accessor: (d: T) => string}\n\nexport interface SearchEvents<T extends SearchData> {\n  [Events.Input]: T[];\n  [Events.Select]: T;\n  [Events.Enter]: {textInput: string; accessor: AccessorOption<T>};\n  [Events.AccessorSelect]: {accessor: AccessorOption<T>; index: number};\n}\n"], "names": ["Events"], "mappings": "IAAYA,GAAZ,SAAYA,GACVA,EAAA,MAAA,QACAA,EAAA,OAAA,SACAA,EAAA,MAAA,QACAA,EAAA,eAAA,gBACD,CALD,CAAYA,IAAAA,EAKX,CAAA"}