var r;function e(e,t){void 0===t&&(t=!1);var s,o=e.CSS;if("boolean"==typeof r&&!t)return r;if(!(o&&"function"==typeof o.supports))return!1;var p=o.supports("--css-vars","yes"),a=o.supports("(--css-vars: yes)")&&o.supports("color","#00000000");return s=p||a,t||(r=s),s}function t(r,e,t){if(!r)return{x:0,y:0};var s,o,p=e.x,a=e.y,u=p+t.left,n=a+t.top;if("touchstart"===r.type){var c=r;s=c.changedTouches[0].pageX-u,o=c.changedTouches[0].pageY-n}else{var f=r;s=f.pageX-u,o=f.pageY-n}return{x:s,y:o}}export{t as getNormalizedEventCoords,e as supportsCssVariables};
//# sourceMappingURL=util.js.map
